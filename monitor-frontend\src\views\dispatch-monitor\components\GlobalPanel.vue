<template>
  <div class="global-panel y-container no-padding">
    <div class="globalitem">
      <div class="globalitemleft">
        <img src="@/assets/images/totalCalls.png" alt="" class="imgwh1" />
        <div class="globalitemleftarea">
          <div class="globalitemleftword">接报总量</div>
          <div class="globalitemleftmsg">{{ data.totalCalls }}</div>
        </div>
      </div>
      <div class="globalitemright">
        <div v-for="(item, index) in showList1" :key="index" class="globalitemrightpart">
          <div class="line"></div>
          <img :src="require('@/assets/images/' + item.name + '.png')" alt="" class="imgwh2" />
          <div class="globalitemrightpartname">{{ item.label }}</div>
          <div class="globalitemrightpartlabel">{{ data[item.name] }}</div>
        </div>
      </div>
    </div>
    <div class="globalitem globalitemtop">
      <div class="globalitemleft">
        <img src="@/assets/images/totalCalls.png" alt="" class="imgwh1" />
        <div class="globalitemleftarea">
          <div class="globalitemleftword">任务总量</div>
          <div class="globalitemleftmsg">{{ data.totalTasks }}</div>
        </div>
      </div>
      <div class="globalitemright">
        <div v-for="(item, index) in showList2" :key="index" class="globalitemrightpart">
          <div class="line"></div>
          <img :src="require('@/assets/images/' + item.name + '.png')" alt="" class="imgwh2" />
          <div class="globalitemrightpartname">{{ item.label }}</div>
          <div class="globalitemrightpartlabel">{{ data[item.name] }}</div>
          <!-- <div class="globalitemrightpartlabel">{{ item.name == "avgTaskDuration" ? data[item.name].slice(0, -3) + "分钟" : data[item.name] }}</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GlobalPanel",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showList1: [
        { label: "直办总量", name: "directCalls" },
        { label: "事件总量", name: "totalEvents" },
        { label: "完结事件", name: "completedEvents" },
      ],
      showList2: [
        { label: "进行任务", name: "inProgressTasks" },
        { label: "办结任务", name: "completedTasks" },
        { label: "任务平均时长", name: "avgTaskDuration" },
      ],
      // showList: [
      //   { label: "接报总量", name: "totalCalls" },
      //   { label: "直办总量", name: "directCalls" },
      //   { label: "事件总量", name: "totalEvents" },
      //   { label: "完结事件", name: "completedEvents" },
      //   { label: "任务总量", name: "totalTasks" },
      //   { label: "进行任务", name: "inProgressTasks" },
      //   { label: "办结任务", name: "completedTasks" },
      //   { label: "任务平均时长", name: "avgTaskDuration" },
      // ],
    };
  },
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.global-panel {
  flex: 0 0 max-content;
  margin-top: 16px;
  margin-bottom: 24px;
  .globalitem {
    border-radius: 4px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    height: 104px;
    width: 424px;
    padding-left: 16px;
    display: flex;
    align-items: center;
    .globalitemleft {
      width: 148px;
      height: 64px;
      display: flex;
      gap: 4px;
      .globalitemleftarea {
        .globalitemleftword {
          font-size: 16px;
          color: #fff;
        }
        .globalitemleftmsg {
          font-size: 24px;
          font-weight: bold;
          color: #00ffff;
          margin-top: 4px;
        }
      }
    }
    .globalitemright {
      display: flex;
      align-items: center;

      .globalitemrightpart {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 88px;
        height: 84px;
        position: relative;
        .line {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 2px;
          background: #00aaff15;
          height: 64px;
        }
        .globalitemrightpartname {
          color: #fff;
          font-size: 14px;
        }
        .globalitemrightpartlabel {
          font-size: 20px;
          font-weight: bold;
          color: #00aaff;
          // text-overflow: ellipsis;
          // overflow: hidden;
          // white-space: nowrap;
          width: 88px;
          text-align: center;
          margin-top: 4px;
        }
      }
    }
  }
  .globalitemtop {
    margin-top: 16px;
  }
  // .global-panel_content {
  //   @include full;
  //   display: grid;
  //   grid-template-columns: repeat(4, 1fr);
  //   gap: 24px 0;

  //   .global-panel_item {
  //     @include flex-row;
  //     justify-content: flex-start;

  //     .global-panel_item_info {
  //       @include flex-col;
  //       align-items: flex-start;

  //       .label {
  //         font-size: 14px;
  //         color: $txtColor-reverse;
  //       }

  //       .count {
  //         font-size: 24px;
  //         font-weight: bold;
  //         background: linear-gradient(180deg, #ffffff 8%, #ffe4aa 62%, #fbac06 95%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //         background-clip: text;
  //         text-fill-color: transparent;
  //       }
  //     }
  //   }
  // }
}
.imgwh2 {
  width: 64px;
  height: 64px;
}
.imgwh2 {
  width: 32px;
  height: 32px;
  margin-bottom: 4px;
}
</style>
