// y系列公共样式

// 页面
.#{$namespace}-page {
  @include full;
}

// 通用bar
.#{$namespace}-bar {
  @include bar;

  &.no-padding {
    padding: 0;
  }

  >div {

    +div,
    +span,
    +button {
      margin-left: 16px;
    }
  }

  >span {
    +div {
      margin-left: 8px;
    }
  }
}

.#{$namespace}-header {
  @include bar;
  border-bottom: 1px solid $borderColor;

  .#{$namespace}-title {
    flex: 1;
    white-space: nowrap;
  }
}

.#{$namespace}-footer {
  @include bar;
  border-top: 1px solid $borderColor;
}

// 普通容器，可以配合no-padding类名
.#{$namespace}-container {
  @include container;
  overflow: hidden;

  &.no-padding {
    padding: 0;
  }
}

// 限制大小overflow:auto的容器，可以配合no-padding类名
.#{$namespace}-container--tight {
  @include container;
  overflow: auto;

  &.no-padding {
    padding: 0;
  }
}

// 标题
.#{$namespace}-title {
  @include title;
}

// 前面有竖线的标题
.#{$namespace}-title--secondary {
  @include title;
  position: relative;
  margin-left: 12px;
  font-weight: 400;
  color: $txtColor;

  &:before {
    content: '';
    position: absolute;
    left: -12px;
    width: 4px;
    height: 100%;
    background-color: $themeColor;
  }
}

// table标签专用，模仿el-table样式
.#{$namespace}-table {
  overflow: hidden;
  flex: unset;
  border: 1px solid $borderColor;
  border-radius: 4px;

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      border-right: 1px solid $borderColor;
      border-bottom: 1px solid $borderColor;
    }

    .no-bb {
      border-bottom: none;
    }

    .no-br {
      border-right: none;
    }
  }

  .cell span {
    font-size: 14px;
    font-weight: normal;
    color: $txtColor;
  }
}

// 卡片布局
.#{$namespace}-card-wrapper {
  display: grid;
  justif#{$namespace}-content: space-around;
  gap: 16px;
  flex-wrap: wrap;
  overflow: unset;
  overflow-y: auto;
}

// 无数据
.#{$namespace}-no-data {
  display: block;
  width: 100%;
  text-align: center;
  color: $txtColor-light
}

// 复选框
.#{$namespace}-checkbox {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 20px;
  height: 20px;
  border-radius: 50px;
  background: $bgColor-dark;
  border: 1px solid #c5c5c5;
  cursor: pointer;
  z-index: 99;

  .svg-icon {
    display: none;
    position: absolute;
    top: 2px;
    left: 3px;
    font-size: 12px;
    color: $txtColor-reverse;
  }

  &.checked {
    background-color: $themeColor;
    border-color: $themeColor;

    .svg-icon {
      display: block;
    }
  }
}

// 可增删的input组
.#{$namespace}-multi-input {
  @include flex-col;
  width: 100%;

  .#{$namespace}-multi-input_item {
    @include flex-row;
    width: 100%;

    >div {
      flex: 1;

      +div {
        margin-left: 8px;
      }
    }

    .el-button {
      margin-left: 8px;
      padding: 12px;
    }

    +.#{$namespace}-multi-input_item {
      margin-top: 16px;
    }
  }
}

// 前缀
.#{$namespace}-prefixer {
  font-size: 14px;
  font-weight: 400;
  color: $txtColor-light;
}

.#{$namespace}-sign {
  &::before {
    content: '';
    display: inline-block;
    margin-right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(180deg, #FFFFFF 0%, $themeColor 100%);
    box-shadow: 0px 0px 10px 0px $themeColor;
  }

  &.primary::before {
    background: linear-gradient(180deg, #FFFFFF 0%, $themeColor 100%);
    box-shadow: 0px 0px 10px 0px $themeColor;
  }

  &.danger::before {
    background: linear-gradient(180deg, #FFFFFF 0%, $color-danger 100%);
    box-shadow: 0px 0px 10px 0px $color-danger;
  }

  &.warning::before {
    background: linear-gradient(180deg, #FFFFFF 0%, $color-warning 100%);
    box-shadow: 0px 0px 10px 0px $color-warning;
  }

  &.success::before {
    background: linear-gradient(180deg, #FFFFFF 0%, $color-success 100%);
    box-shadow: 0px 0px 10px 0px $color-success;
  }

  &.info::before {
    background: linear-gradient(180deg, #FFFFFF 0%, $color-info 100%);
    box-shadow: 0px 0px 10px 0px $color-info;
  }
}