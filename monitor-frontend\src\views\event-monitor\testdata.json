{"clientId": "eventMonitor:2107c2d30ddd49a5a8b3b75ef548bbac", "type": "eventMonitor", "data": {"userLocationInfoList": [{"member": {"createTime": "2024-12-31 15:27:44", "idcard": "110105200001011000", "lat": "23.126957", "lon": "113.366811", "memberName": "陈晓萍", "phone": "18203692793", "sex": "1", "teamDentity": "4", "teamMemberId": "82643699358329638545647", "teamRecordId": "82643699359909638663692", "updateTime": "2024-12-31 15:27:44"}, "location": {"lat": "23.126957", "lon": "113.366811"}, "lastTimestamp": "2025-01-10 09:54:37.902"}], "taskInfoList": [{"taskId": "9d377f57180444509d122f55017d18d3", "taskName": "救援林小盛任务", "teamName": "郑州市中心医院急救站", "kilometer": 1457734.0, "leaderName": "陈晓萍", "expectedTime": 56206.0, "lon": "113.56393005057087", "lat": "34.78797352978875"}, {"taskId": "9d377f57180444509d122f55017d18d3", "taskName": "救援林小盛任务", "teamName": "郑州市中心医院急救站", "kilometer": 1457734.0, "leaderName": "陈晓萍", "expectedTime": 56206.0, "lon": "113.56393005057087", "lat": "34.78797352978875"}, {"taskId": "9d377f57180444509d122f55017d18d3", "taskName": "救援林小盛任务", "teamName": "郑州市中心医院急救站", "kilometer": 1457734.0, "leaderName": "陈晓萍", "expectedTime": 56206.0, "lon": "113.56393005057087", "lat": "34.78797352978875"}], "dispatchTaskTeamInfoDTOList": [{"jrRescueTeam": {"teamCode": "102894", "teamRecordId": "82643699359909638663692", "teamName": "郑州市中心医院急救站", "teamType": null, "leadName": "陈晓萍", "openid": null, "leadMobile": "18203692793", "leadLoginPwd": null, "areaCode": "410102", "auditStatus": 20, "teamStatus": 10, "infoUpdateFlag": "N", "createTime": "2024-12-31 15:27:44", "taskCount": 0, "delFlag": "N", "delTime": null, "delOper": null, "auditPushSmsYime": "2024-12-31 15:27:44", "lastUpdateTime": "2024-12-31 15:27:44", "teamBusiType": 4, "deptTd": "410102", "userId": "82643706929729660639704"}, "teamLon": "113.60624964124433", "teamLat": "34.75151485568372", "distance": 5.6015453, "duration": null, "vehicleNum": 4, "jrRescueTeamEquips": [{"teamEquipId": "82643662980619548269738", "teamRecordId": "82643699359909638663692", "equipName": "消毒设备", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:28:21", "updateTime": "2024-12-31 16:28:21"}, {"teamEquipId": "82643663084199548579128", "teamRecordId": "82643699359909638663692", "equipName": "防护用具", "equipNum": 100, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:28:11", "updateTime": "2024-12-31 16:28:11"}, {"teamEquipId": "82643663178289548812880", "teamRecordId": "82643699359909638663692", "equipName": "急救药品", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:28:02", "updateTime": "2024-12-31 16:28:02"}, {"teamEquipId": "82643663291379549212243", "teamRecordId": "82643699359909638663692", "equipName": "骨折固定夹板", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:50", "updateTime": "2024-12-31 16:27:50"}, {"teamEquipId": "82643663381899549573495", "teamRecordId": "82643699359909638663692", "equipName": "担架", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:41", "updateTime": "2024-12-31 16:27:41"}, {"teamEquipId": "82643663476899549875359", "teamRecordId": "82643699359909638663692", "equipName": "便携式呼吸机", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:32", "updateTime": "2024-12-31 16:27:32"}, {"teamEquipId": "82643663586189550258572", "teamRecordId": "82643699359909638663692", "equipName": "简易呼吸器（球囊面罩）", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:21", "updateTime": "2024-12-31 16:27:21"}, {"teamEquipId": "82643663711379550685716", "teamRecordId": "82643699359909638663692", "equipName": "便携式氧气瓶与氧气袋", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:08", "updateTime": "2024-12-31 16:27:08"}, {"teamEquipId": "82643663787529550814661", "teamRecordId": "82643699359909638663692", "equipName": "心电监护设备", "equipNum": 5, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:01", "updateTime": "2024-12-31 16:27:01"}, {"teamEquipId": "82643663886559551234677", "teamRecordId": "82643699359909638663692", "equipName": "心肺复苏按压板", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:26:51", "updateTime": "2024-12-31 16:26:51"}, {"teamEquipId": "82643664005069551675327", "teamRecordId": "82643699359909638663692", "equipName": "手动除颤仪", "equipNum": 5, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:26:39", "updateTime": "2024-12-31 16:26:39"}, {"teamEquipId": "82643664079979551855202", "teamRecordId": "82643699359909638663692", "equipName": "自动体外除颤器（AED）", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:26:32", "updateTime": "2024-12-31 16:26:32"}], "jrRescueTeamMembers": [{"teamMemberId": "82643668464529562461296", "teamRecordId": "82643699359909638663692", "memberName": "赵灵儿", "sex": "1", "phone": "13580429793", "teamDentity": "2", "partyMemberFlag": null, "idcard": "******************", "memberAbilitySum": "7,4", "qualificationFiles": "82643668482729562566928", "emergencyName": "", "emergencyMobile": "", "createTime": "2024-12-31 16:19:13", "updateTime": "2024-12-31 16:19:13"}, {"teamMemberId": "82643668923379563677327", "teamRecordId": "82643699359909638663692", "memberName": "傅益生", "sex": "0", "phone": "13580429792", "teamDentity": "2", "partyMemberFlag": null, "idcard": "******************", "memberAbilitySum": "4,7", "qualificationFiles": "82643668943799563878585", "emergencyName": "", "emergencyMobile": "", "createTime": "2024-12-31 16:18:27", "updateTime": "2024-12-31 16:18:27"}, {"teamMemberId": "82643669385499564924559", "teamRecordId": "82643699359909638663692", "memberName": "陈义生", "sex": "0", "phone": "13580429791", "teamDentity": "2", "partyMemberFlag": null, "idcard": "******************", "memberAbilitySum": "4,7", "qualificationFiles": "82643669442649565135824", "emergencyName": "", "emergencyMobile": "", "createTime": "2024-12-31 16:17:41", "updateTime": "2024-12-31 16:17:41"}, {"teamMemberId": "82643699358329638545647", "teamRecordId": "82643699359909638663692", "memberName": "陈晓萍", "sex": "1", "phone": "18203692793", "teamDentity": "4", "partyMemberFlag": null, "idcard": "110105200001011000", "memberAbilitySum": null, "qualificationFiles": null, "emergencyName": null, "emergencyMobile": null, "createTime": "2024-12-31 15:27:44", "updateTime": "2024-12-31 15:27:44"}], "leaderName": "陈晓萍", "teamTotalMember": 4, "taskTotalMember": 3, "taskName": "救援林小盛任务", "taskLocation": "郑州市中原区郑州京华机械制造公司东北约69米", "taskLon": "113.56393005057087", "taskLat": "34.78797352978875"}, {"jrRescueTeam": {"teamCode": "102894", "teamRecordId": "82643699359909638663692", "teamName": "郑州市中心医院急救站", "teamType": null, "leadName": "陈晓萍", "openid": null, "leadMobile": "18203692793", "leadLoginPwd": null, "areaCode": "410102", "auditStatus": 20, "teamStatus": 10, "infoUpdateFlag": "N", "createTime": "2024-12-31 15:27:44", "taskCount": 0, "delFlag": "N", "delTime": null, "delOper": null, "auditPushSmsYime": "2024-12-31 15:27:44", "lastUpdateTime": "2024-12-31 15:27:44", "teamBusiType": 4, "deptTd": "410102", "userId": "82643706929729660639704"}, "teamLon": "113.60624964124433", "teamLat": "34.75151485568372", "distance": 5.6015453, "duration": null, "vehicleNum": 4, "jrRescueTeamEquips": [{"teamEquipId": "82643662980619548269738", "teamRecordId": "82643699359909638663692", "equipName": "消毒设备", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:28:21", "updateTime": "2024-12-31 16:28:21"}, {"teamEquipId": "82643663084199548579128", "teamRecordId": "82643699359909638663692", "equipName": "防护用具", "equipNum": 100, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:28:11", "updateTime": "2024-12-31 16:28:11"}, {"teamEquipId": "82643663178289548812880", "teamRecordId": "82643699359909638663692", "equipName": "急救药品", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:28:02", "updateTime": "2024-12-31 16:28:02"}, {"teamEquipId": "82643663291379549212243", "teamRecordId": "82643699359909638663692", "equipName": "骨折固定夹板", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:50", "updateTime": "2024-12-31 16:27:50"}, {"teamEquipId": "82643663381899549573495", "teamRecordId": "82643699359909638663692", "equipName": "担架", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:41", "updateTime": "2024-12-31 16:27:41"}, {"teamEquipId": "82643663476899549875359", "teamRecordId": "82643699359909638663692", "equipName": "便携式呼吸机", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:32", "updateTime": "2024-12-31 16:27:32"}, {"teamEquipId": "82643663586189550258572", "teamRecordId": "82643699359909638663692", "equipName": "简易呼吸器（球囊面罩）", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:21", "updateTime": "2024-12-31 16:27:21"}, {"teamEquipId": "82643663711379550685716", "teamRecordId": "82643699359909638663692", "equipName": "便携式氧气瓶与氧气袋", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:08", "updateTime": "2024-12-31 16:27:08"}, {"teamEquipId": "82643663787529550814661", "teamRecordId": "82643699359909638663692", "equipName": "心电监护设备", "equipNum": 5, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:27:01", "updateTime": "2024-12-31 16:27:01"}, {"teamEquipId": "82643663886559551234677", "teamRecordId": "82643699359909638663692", "equipName": "心肺复苏按压板", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:26:51", "updateTime": "2024-12-31 16:26:51"}, {"teamEquipId": "82643664005069551675327", "teamRecordId": "82643699359909638663692", "equipName": "手动除颤仪", "equipNum": 5, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:26:39", "updateTime": "2024-12-31 16:26:39"}, {"teamEquipId": "82643664079979551855202", "teamRecordId": "82643699359909638663692", "equipName": "自动体外除颤器（AED）", "equipNum": 10, "equipBrand": "", "specification": "", "equipModel": null, "createTime": "2024-12-31 16:26:32", "updateTime": "2024-12-31 16:26:32"}], "jrRescueTeamMembers": [{"teamMemberId": "82643668464529562461296", "teamRecordId": "82643699359909638663692", "memberName": "赵灵儿", "sex": "1", "phone": "13580429793", "teamDentity": "2", "partyMemberFlag": null, "idcard": "******************", "memberAbilitySum": "7,4", "qualificationFiles": "82643668482729562566928", "emergencyName": "", "emergencyMobile": "", "createTime": "2024-12-31 16:19:13", "updateTime": "2024-12-31 16:19:13"}, {"teamMemberId": "82643668923379563677327", "teamRecordId": "82643699359909638663692", "memberName": "傅益生", "sex": "0", "phone": "13580429792", "teamDentity": "2", "partyMemberFlag": null, "idcard": "******************", "memberAbilitySum": "4,7", "qualificationFiles": "82643668943799563878585", "emergencyName": "", "emergencyMobile": "", "createTime": "2024-12-31 16:18:27", "updateTime": "2024-12-31 16:18:27"}, {"teamMemberId": "82643669385499564924559", "teamRecordId": "82643699359909638663692", "memberName": "陈义生", "sex": "0", "phone": "13580429791", "teamDentity": "2", "partyMemberFlag": null, "idcard": "******************", "memberAbilitySum": "4,7", "qualificationFiles": "82643669442649565135824", "emergencyName": "", "emergencyMobile": "", "createTime": "2024-12-31 16:17:41", "updateTime": "2024-12-31 16:17:41"}, {"teamMemberId": "82643699358329638545647", "teamRecordId": "82643699359909638663692", "memberName": "陈晓萍", "sex": "1", "phone": "18203692793", "teamDentity": "4", "partyMemberFlag": null, "idcard": "110105200001011000", "memberAbilitySum": null, "qualificationFiles": null, "emergencyName": null, "emergencyMobile": null, "createTime": "2024-12-31 15:27:44", "updateTime": "2024-12-31 15:27:44"}], "leaderName": "陈晓萍", "teamTotalMember": 4, "taskTotalMember": 3, "taskName": "救援林小盛任务", "taskLocation": "郑州市中原区郑州京华机械制造公司东北约69米", "taskLon": "113.56393005057087", "taskLat": "34.78797352978875"}], "taskRecordDetail": [{"id": 1874032804014931968, "taskId": "9d377f57180444509d122f55017d18d3", "logMemo": "完成任务", "logType": "4", "logTimestamp": 1735639212979, "logTime": "2024-12-31 18:00:12", "logUser": "18203692793", "logFile": null, "taskTitle": "救援林小盛任务", "logVideoFile": null}, {"id": 1874032803868131328, "taskId": "9d377f57180444509d122f55017d18d3", "logMemo": "抢救成功，准备送回医院", "logType": null, "logTimestamp": 1735639212944, "logTime": "2024-12-31 18:00:12", "logUser": "18203692793", "logFile": "1874032769999126528", "taskTitle": "救援林小盛任务", "logVideoFile": "1874032797627006976"}, {"id": 1874032662197125120, "taskId": "9d377f57180444509d122f55017d18d3", "logMemo": "使用 AED 进行急救", "logType": null, "logTimestamp": 1735639179167, "logTime": "2024-12-31 17:59:39", "logUser": "18203692793", "logFile": "1874032592995303424", "taskTitle": "救援林小盛任务", "logVideoFile": "1874032659122700288"}, {"id": 1874032484987781120, "taskId": "9d377f57180444509d122f55017d18d3", "logMemo": "准备实施救援", "logType": null, "logTimestamp": 1735639136917, "logTime": "2024-12-31 17:58:56", "logUser": "18203692793", "logFile": "1874032427332878336", "taskTitle": "救援林小盛任务", "logVideoFile": "1874032481103855616"}, {"id": 1874032262689669120, "taskId": "9d377f57180444509d122f55017d18d3", "logMemo": "达到目的地", "logType": "5", "logTimestamp": 1735639083917, "logTime": "2024-12-31 17:58:03", "logUser": "18203692793", "logFile": null, "taskTitle": "救援林小盛任务", "logVideoFile": null}, {"id": 1874031905892810752, "taskId": "9d377f57180444509d122f55017d18d3", "logMemo": "开始执行任务", "logType": "4", "logTimestamp": 1735638998850, "logTime": "2024-12-31 17:56:38", "logUser": "18203692793", "logFile": null, "taskTitle": "救援林小盛任务", "logVideoFile": null}], "taskRecord": [{"rescueTaskId": "9d377f57180444509d122f55017d18d3", "createTime": "2024-12-31 17:53:26", "taskTitle": "救援林小盛任务", "taskStatus": "1"}, {"rescueTaskId": "9d377f57180444509d122f55017d18d3", "createTime": "2024-12-31 17:55:31", "taskTitle": "救援林小盛任务", "taskStatus": "2"}, {"rescueTaskId": "9d377f57180444509d122f55017d18d3", "createTime": "2024-12-31 17:56:38", "taskTitle": "救援林小盛任务", "taskStatus": "4"}, {"rescueTaskId": "9d377f57180444509d122f55017d18d3", "createTime": "2024-12-31 17:58:03", "taskTitle": "救援林小盛任务", "taskStatus": "5"}, {"rescueTaskId": "9d377f57180444509d122f55017d18d3", "createTime": "2024-12-31 18:00:12", "taskTitle": "救援林小盛任务", "taskStatus": "3"}], "aroundMgrList": [{"id": "0cf31af2810140b3950a42f88bb5060b", "eqName": "球形摄像机-PIO-311", "eqIp": "localhost", "eqPort": "8081", "eqLon": "113.59043518749861", "eqLat": "34.81751794684958", "eqCode": "123456987", "eqPwd": "321456", "eqType": 0, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-10 10:05:26", "location": "郑州市中原区翠竹街东南约239米", "creator": "超级管理员"}, {"id": "b9ed9a5ee9dc4c61bb3e834bdda5f9a1", "eqName": "圆形摄像头-BBB-221", "eqIp": "127.0.0.1", "eqPort": "8080", "eqLon": "113.50872941077007", "eqLat": "34.76482115057719", "eqCode": "123465", "eqPwd": "166666", "eqType": 0, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-10 10:09:15", "location": "郑州市中原区须水镇第六小学东北约110米", "creator": "超级管理员"}, {"id": "e4ab2ed69fbc4567855012443bcb50b8", "eqName": "圆形摄像头-LIO-069", "eqIp": "127.0.0.1", "eqPort": "8080", "eqLon": "113.56331874391032", "eqLat": "34.78779342811324", "eqCode": "13456123", "eqPwd": "123456", "eqType": 0, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-07 14:13:00", "location": "郑州市中原区郑州京华机械制造公司", "creator": "超级管理员"}], "dynamicMgrList": [{"id": "45aad6b2a5aa4a1d8ac584a58b12b9c2", "eqName": "军用无人机-C", "eqIp": "127.0.0.1", "eqPort": "8085", "eqLon": "113.5184753574895", "eqLat": "34.759544548761085", "eqCode": "555555", "eqPwd": "555555", "eqType": 2, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-10 10:13:13", "location": "郑州市中原区须水大街28号朝阳小区西北约97米", "creator": "超级管理员"}, {"id": "46c8ffb88114444aa7a1c615dada8dcc", "eqName": "军用指挥车", "eqIp": "127.0.0.1", "eqPort": "8080", "eqLon": "113.56331874391032", "eqLat": "34.78779342811324", "eqCode": "666666", "eqPwd": "666666", "eqType": 1, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-07 14:15:34", "location": "郑州市中原区郑州京华机械制造公司", "creator": "超级管理员"}, {"id": "4ae3ce3443244c63bfd31d26a68f2a20", "eqName": "军用无人机-B", "eqIp": "127.0.0.1", "eqPort": "8082", "eqLon": "113.57586283179836", "eqLat": "34.78463704273358", "eqCode": "333333", "eqPwd": "333333", "eqType": 2, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-10 10:12:33", "location": "郑州市中原区瑞达路与化工路交叉口北400米路东怡景花园东南约61米", "creator": "超级管理员"}, {"id": "c18cb90926cf49bd9a2941ebfd93cae5", "eqName": "军用指挥车-C`", "eqIp": "127.0.0.1", "eqPort": "8086", "eqLon": "113.59009633167508", "eqLat": "34.75207935402274", "eqCode": "555555", "eqPwd": "555555", "eqType": 1, "countyCode": null, "areaCode": "410102", "cityCode": "410100", "provinceCode": "410000", "enableFlag": "Y", "createTime": "2025-01-10 10:10:57", "location": "郑州市中原区中原西路街道中原中路113号院2号楼中原区向阳小学东北约174米", "creator": "超级管理员"}]}}