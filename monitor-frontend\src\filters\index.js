export function formatFigure(n, symbol = ',') {
  if (!n) {
    return '0'
  }
  const parts = Number(n).toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, symbol)
  return parts.join('.')
}

export function splitNumber(n, symbol = ',') {
  const parts = n.toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, symbol)
  return parts.join('.')
}

export function formatDate(date) {
  const weekdays = [
    '星期日',
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
  ]
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  const weekday = weekdays[date.getDay()]
  const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${weekday}`
  return formattedDate
}