<template>
  <div class="title-block">
    <h4 class="title-block_title blaze-text1">{{ title }}</h4>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.title-block {
  min-width: 280px;
  background: url("@/assets/images/title-block-bg.png") no-repeat center center/100% 100%;
  width: 424px;
  height: 40px;
  line-height: 40px;
  padding-left: 36px;
  .title-block_title {
    font-size: 20px;
    font-weight: bold;
  }
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
</style>
