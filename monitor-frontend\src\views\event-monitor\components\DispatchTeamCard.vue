<template>
  <div class="card-box">
    <div>
      <div class="card-box-header">
        <div class="card-box-img">
          <img src="../images/icon-yinji.png" alt="" />
        </div>
        <div class="card-box-info">
          <div class="card-box-title blaze-text">
            {{ item.jrRescueTeam.teamName }}
          </div>
          <div class="card-box-tags">
            <span class="tag">
              <font class="blaze-text">{{ tag }}</font>
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="card-box-detail">
      <table class="event-table">
        <tr>
          <th>车辆情况</th>
          <td colspan="3">
            <span class="event-table-tag"
              >车辆总数
              <font style="font-size: 20px" class="blaze-text3">{{
                item.vehicleNum
              }}</font>
            </span>
            <span class="event-table-tag"
              >人员总数
              <font style="font-size: 20px" class="blaze-text3">{{
                item.jrRescueTeamMembers.length
              }}</font>
            </span>
            <!-- <span class="event-table-tag">空闲车辆 <font class="blaze-text2">1</font></span>
            <span class="event-table-tag">已用车辆 <font class="blaze-text2">2</font></span> -->
          </td>
        </tr>
        <tr>
          <th>装备情况</th>
          <td colspan="3">
            <span
              v-for="(item2, index2) in item.jrRescueTeamEquips"
              :key="index2"
              class="event-table-tag"
              >{{ item2.equipName
              }}<font style="font-size: 24px; display: none" class="blaze-text2"
                >{{ item2.equipNum }}
              </font></span
            >
          </td>
        </tr>
        <tr>
          <th>人员情况</th>
          <td colspan="3">
            <span
              v-for="(item2, index2) in item.jrRescueTeamMembers"
              :key="index2"
              class="event-table-tag"
              >{{ item2.memberName }}</span
            >
          </td>
        </tr>
      </table>
      <div class="event-distance blaze-text2">
        距
        <font style="font-size: 24px" class="">{{
          item.distance.toFixed(2)
        }}</font>
        KM
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DispatchTeamCard",
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    dict: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  computed: {
    tag() {
      if (!this.dict) return "";
      if (!this.item?.jrRescueTeam?.teamBusiType) return "";

      return this.dict[this.item.jrRescueTeam.teamBusiType];
    },
  },
};
</script>

<style scoped lang="scss">
/* Add any specific styles for this component here */
/* You might want to move relevant styles from the parent component */
.card-box {
  position: relative;
  min-height: 170px;
  padding: 16px 30px 16px 16px;
  margin-bottom: 16px;
  background: linear-gradient(
    270deg,
    rgba(4, 203, 216, 0.05) 0%,
    rgba(4, 203, 216, 0.1) 100%
  );

  .card-box-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .card-box-img {
    margin-right: 14px;
    flex-shrink: 0;
  }

  .card-box-info {
    flex: 1;
  }

  .card-box-title {
    font-size: 18px;

    img {
      margin-right: 8px;
    }
  }

  .card-box-tags {
    .tag {
      position: relative;
      background: rgba(4, 203, 216, 0.1);
      line-height: 24px;
      padding: 0 7px;
      margin-right: 8px;

      &:after {
        right: 0;
        content: "";
        position: absolute;
        width: 2px;
        height: 12px;
        background: linear-gradient(
          rgba(255, 255, 255, 1),
          rgba(4, 203, 216, 1)
        );
        top: 50%;
        margin-top: -6px;
      }

      &:before {
        left: 0;
        content: "";
        position: absolute;
        width: 2px;
        height: 12px;
        background: linear-gradient(
          rgba(255, 255, 255, 1),
          rgba(4, 203, 216, 1)
        );
        top: 50%;
        margin-top: -6px;
      }
    }
  }
}

.event-table {
  width: 330px;
  font-size: 14px;
  color: #fff;
  vertical-align: top;

  td,
  th {
    padding: 5px 7px;
    vertical-align: top;
    line-height: 24px;
  }

  th {
    color: #5d8c8f;
    width: 70px;
  }

  &-tag {
    display: inline-flex;
    align-items: center;
    position: relative;
    padding-right: 30px;

    font {
      margin-left: 8px;
    }

    &:after {
      content: "";
      display: inline-block;
      vertical-align: middle;
      position: absolute;
      right: 16px;
      top: 2px;
      opacity: 0.2;
      height: 16px;
      width: 1px;
      background: #04cbd8;
    }

    &:last-child {
      padding-right: 0;

      &:after {
        display: none;
      }
    }
  }
}

.event-distance {
  position: absolute;
  right: 15px;
  top: 120px;
}

.blaze-text2 {
  color: #ffdc00;
}

.blaze-text3 {
  background: #00ffff;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
</style>
