<template>
  <!-- <div
    v-if="item.eqType != 1"
    :id="'marker-' + item.id"
    :type="item.eqType"
    class="map-pop"
    :style="css"
  >
    <div @click="closeWindow" title="关闭" class="map-pop-close"></div>
    <div class="map-pop-video"></div>
    <div class="map-pop-title">{{ item.eqName }}</div>
    <div class="map-pop-info"><span>所在位置: </span>{{ item.location }}</div>
    <div @click="makecall()" class="map-pop-btn">
      <span><font>视频上墙</font></span>
    </div>
  </div>
  <div
    v-else
    :id="'marker-' + item.id"
    type="xingdongzhidui"
    class="map-pop2"
    :style="css"
  >
    <div class="map-pop-header">
      <div class="map-pop-title"><span class="blaze-text">行动支队</span></div>
      <div @click="closeWindow" title="关闭" class="map-pop-close"></div>
    </div>
    <div class="map-pop-content">
      <div class="map-pop-title2">{{ item.eqName }}</div>
      <div class="map-pop-info"><span>任务名称: </span>{{ item.taskName }}</div>
      <div class="map-pop-info"><span>所在位置: </span>{{ item.location }}</div>
      <div class="map-pop-info"><span>目的地经纬度: </span>{{ item.location }}</div>
    </div>
  </div> -->

  <div
    :id="'marker-' + item.id"
    :type="item.eqType"
    class="map-pop2"
    :style="css"
  >
    <!-- 使用图片实现箭头 -->
    <!-- <div class="arrow-container">
      <img src="@/assets/images/info-arrow.png" alt="箭头" class="info-arrow">
    </div> -->
    
    <!-- 非行动支队类型 -->
    <template v-if="item.eqType != 1">
      <div class="map-pop-title">
        <span >{{ typeName }}</span>
        <span>
          <img src="../images/close.svg" alt="放大" @click="full=!full" style="margin-right: 20px;" />
          <img src="../images/close-button.png" alt="关闭" @click="closeWindow" />
        </span>
        
      </div>
      <div class="map-pop-content">
        <div class="map-pop-video">
          <div v-if="showVideo" :class="full?'full':''" class="webrtc-remote-video">
            <!-- <div class="webrtc-remote-video-loading"></div> -->
             <FullVideo :item="item" :class="full?'':'mini'"/>
          </div>

          <el-image v-else :src="previewImage">
            <template #error>
              <div></div>
            </template>
          </el-image>
        </div>
        <div class="map-pop-info">
          <img class="hand-icon" src="../images/svg-hand.svg" alt="" />
          <span class="heavey-text">{{ item.eqName }}</span>
        </div>
        <div class="map-pop-info">
          <span class="label">所在位置: </span>{{ item.location }}
        </div>
      </div>
      <div class="map-pop-footer">
        <div class="map-pop-btn" @click="handleVideoWall">
          <span><font>视频上墙</font></span>
        </div>
      </div>
    </template>

    <!-- 行动支队类型 -->
    <template v-else>
      <div class="map-pop-header">
        <div class="map-pop-title">
          <span class="blaze-text">{{ typeName }}</span>
          <img src="../images/close-button.png" alt="关闭" @click="closeWindow" />
        </div>
      </div>
      <div class="map-pop-content">
        <div class="map-pop-title2">{{ item.eqName }}</div>
        <div class="map-pop-info">
          <span class="label">任务名称: </span>{{ item.taskName }}
        </div>
        <div class="map-pop-info">
          <span class="label">目的地经纬度: </span>{{ lonAndlat }}
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import WebrtcClient from "@/utils/webrtc.js";
import eventBus from "@/utils/eventbus.js";
import FullVideo from "./FullVideo.vue";

export default {
  name: 'InfoWindow',
  props: {
    position: {
      type: Object,
      required: true
    },
    item: {
      type: Object,
      required: true
    },
    marker: {
      type: Object,
      required: true
    }
  },
  components: {
    FullVideo
  },
  data(){
    return {
      showVideo:false,
      full:false
    }
  },
  mounted() {
    if(this.item.eqType != 1){
      this.handleVideoWall()
    }
  },
  beforeDestroy() {
    if(this.item.eqType != 1){
      try{
      //  WebrtcClient.clearCall();
      }catch(e){console.log(e)}
      
    }
    
  },
  computed: {
    css() {
      return {
        left: `${this.position.left - 160}px`,
        top: `${this.position.top - 400}px`
      };
    },

    lonAndlat() {
      return `${this.item.lonStr}、${this.item.latStr}`;
    },

    typeName() {
      const typeMap = {
        0: "摄像头",
        1: "行动支队",
        2: "无人机"
      };
      return typeMap[this.item.eqType] || "";
    },

    previewImage() {
      return `images/pv/${this.item.index + (this.item.eqType)}.png`;
    }
  },

  methods: {
    handleVideoWall() {
      let _self = this;
      console.log('handleVideoWall',this.item.id || "34020000001320000002",this.item)
      eventBus.$emit("webrtcVideoClickTypeChange","infowindow");
      WebrtcClient.makecall(this.item.eqCode || "34020000001320000002",function(){
        _self.showVideo = true;
      });
      this.$emit("toWall", this.item);
    },

    closeWindow() {
      WebrtcClient.clearCall();
      this.$emit("close");
    }
  }
};
</script>

<style lang="scss" scoped>
.map-pop {
  width: 420px;
  height: 400px;
  position: absolute;
  z-index: 333;
  top: 10px;
  left: 10px;
  padding: 70px 40px 20px 40px;
  .map-pop-video {
    width: 300px;
    height: 160px;
    border-radius: 4px;
    background-color: #000;
    margin: 0 auto 10px;
  }
  .map-pop-btn {
    cursor: pointer;
    width: 108px;
    height: 32px;
    border-radius: 4px;
    text-align: center;
    line-height: 32px;
    position: absolute;
    right: 22px;
    bottom: 32px;
    z-index: 2;
  }
  .map-pop-info {
    font-size: 14px;
    color: #fff;
  }
  .map-pop-close {
    position: absolute;
    right: 20px;
    top: 20px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    z-index: 2;
  }

  &[type="2"] {
    background: url(../images/box-zhongduan.png) no-repeat;
    background-size: 100% 100%;
    .map-pop-btn {
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.05) 0%,
        rgba(0, 170, 255, 0.5) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(0, 170, 255, 0.2) 0%,
          #00aaff 100%
        )
        1;
      span {
        background: url(../images/svg-wrj3.svg) no-repeat left center;
        padding-left: 24px;
      }
    }
    .map-pop-info {
      span {
        color: rgba(255, 255, 255, 0.5);
      }
    }
    .map-pop-title {
      color: #00aaff;
      background: url(../images/svg-hand.svg) no-repeat left center;
    }
  }

  &[type="3"] {
    background: url(../images/box-zhongduan.png) no-repeat;
    background-size: 100% 100%;
    .map-pop-btn {
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.05) 0%,
        rgba(0, 170, 255, 0.5) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(0, 170, 255, 0.2) 0%,
          #00aaff 100%
        )
        1;
      span {
        background: url(../images/svg-zhongduan3.svg) no-repeat left center;
        padding-left: 24px;
      }
      // font {
      //   background: linear-gradient(180deg, #ffffff 0%, #0773f0 100%);
      //   -webkit-background-clip: text;
      //   -webkit-text-fill-color: transparent;
      //   background-clip: text;
      //   text-fill-color: transparent;
      // }
    }
    .map-pop-info {
      span {
        color: rgba(255, 255, 255, 0.5);
      }
    }
    .map-pop-title {
      color: #00aaff;
      background: url(../images/svg-hand.svg) no-repeat left center;
    }
  }

  &[type="0"] {
    background: url(../images/box-zhongduan.png) no-repeat;
    background-size: 100% 100%;
    .map-pop-btn {
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.05) 0%,
        rgba(0, 170, 255, 0.5) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(0, 170, 255, 0.2) 0%,
          #00aaff 100%
        )
        1;
      // box-shadow: 0px 4px 10px 0px rgba(69, 245, 143, 0.2),
      //   inset 0px 0px 10px 0px rgba(69, 245, 143, 0.5);
      span {
        background: url(../images/svg-zhongduan3.svg) no-repeat left center;
        padding-left: 24px;
      }
      // font {
      //   background: linear-gradient(180deg, #ffffff 0%, #45f58f 100%);
      //   -webkit-background-clip: text;
      //   -webkit-text-fill-color: transparent;
      //   background-clip: text;
      //   text-fill-color: transparent;
      // }
    }
    .map-pop-info {
      span {
        color: rgba(255, 255, 255, 0.5);
      }
    }
    .map-pop-title {
      color: #00aaff;
      background: url(../images/svg-hand.svg) no-repeat left center;
    }
  }
}

.map-pop2 {
  right: 420px;
  top: 40px;
  position: absolute;
  width: 420px;
  min-height: 400px;
  background: rgba(1, 21, 23, 1);
  color: #fff;
  box-sizing: border-box;
  border: 1px solid rgba(4, 203, 216, 0.8);
  box-shadow: 0px 0px 20px 0px #001112,
    inset 0px 0px 12px 0px rgba(4, 203, 216, 0.5);

  &::before {
    position: absolute;
    content: "";
    left: 0;
    width: 100%;
    height: 10px;
    bottom: 100%;
    left: 0;
    z-index: 2;
    background: url(../images/box-common-header.png) no-repeat center center;
    background-size: 100% 100%;
  }

  &::after {
    position: absolute;
    content: "";
    left: 0;
    width: 100%;
    height: 5px;
    top: 100%;
    left: 0;
    z-index: 2;
    background: url(../images/box-common-footer.png) no-repeat center center;
    background-size: 100% 100%;
  }
  .map-pop-title {
    padding: 0 16px;
    height: 40px;
    line-height: 40px;
    color: #fff;
    background: linear-gradient(
      270deg,
      rgba(0, 170, 255, 0) 0%,
      rgba(0, 170, 255, 0.3) 100%
    );
  }
  .map-pop-title2 {
    font-size: 18px;
    padding-left: 30px;
    margin-bottom: 10px;
    color: #04cbd8;
    background: url(../images/svg-zhidui.svg) no-repeat left center;
  }
  .map-pop-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    span {
      color: rgba(255, 255, 255, 0.5);
    }

    .hand-icon {
      width: 24px;
      height: 24px;
      margin-right: 16px;
    }
  }
  .map-pop-close {
    position: absolute;
    right: 20px;
    top: 20px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    z-index: 2;
  }
  .map-pop-content {
    position: relative;
    // height: 220px;
    background: rgba(1, 20, 29, 0.8);
    padding: 16px;

    display: flex;
    flex-direction: column;
    gap: 8px;

    img {
      width: 100%;
      height: 200px;
    }

    .el-image {
      width: 100%;
      height: 200px;
      background: black;
      border-radius: 4px;
    }

   
    .webrtc-remote-video {
      width: 100%;
      height: 200px;
      background: black;
      border-radius: 4px;
      position: relative;
      &.full{
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
      }
      video{width: 100%; height: 100%;}
    }
  }

  .map-pop-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 16px 16px;

    .map-pop-btn {
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.05) 0%,
        rgba(0, 170, 255, 0.5) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(0, 170, 255, 0.2) 0%,
          #00AAFF 100%
        )
        1;
      border-radius: 4px;
      box-shadow: inset 0px 0px 10px 0px rgba(0, 170, 255, 0.5),
                 0px 4px 10px 0px rgba(0, 170, 255, 0.2);
      
      cursor: pointer;
      width: 108px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      z-index: 2;
      padding: 0;

      span {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding-left: 0;
        
        &::before {
          content: "";
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url('../images/svg-zhongduan3.svg') no-repeat center center;
          background-size: contain;
          margin-right: 4px;
        }
        
        font {
          background: linear-gradient(180deg, #FFFFFF 0%, #00AAFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
}

.map-pop-title {
  font-size: 18px;
  padding-left: 30px;
  color: #fff;

  display: flex;
  align-items: center;
  justify-content: space-between;

  img {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  color: #00aaff !important;
  font-weight: bold;
  font-size: 18px;
}

/* 使用图片的箭头样式 */
// .arrow-container {
//   position: absolute;
//   width: 60px;
//   height: 60px;
//   left: 50%;
//   bottom: -44px;
//   transform: translateX(-50%);
//   z-index: 10;
//   pointer-events: none;
// }

// .info-arrow {
//   width: 100%;
//   height: 100%;
//   object-fit: contain;
// }
</style>
<style>.webrtc-remote-video video{width: 100%;height: 100%;}</style>