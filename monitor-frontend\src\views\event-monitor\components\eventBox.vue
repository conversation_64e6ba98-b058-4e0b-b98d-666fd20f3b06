<template>
  <div class="event-box flex" :style="{ height: height || 'auto' }">
    <div class="event-box-header">
      <TitleBlock :title="title" />
      <div class="event-box-toolbar">
        <slot name="toolbar"></slot>
      </div>
    </div>
    <slot name="sub"></slot>
    <div class="event-box-content flex-item">
      <SeamlessScroll
        ref="seamlessScrollRef"
        :data="data"
        :classOption="options"
        :scrollable="scrollable"
        :style="height"
      >
        <slot></slot>
      </SeamlessScroll>
    </div>
    <div class="event-box-footer"></div>
  </div>
</template>

<script>
export default {
  props: ["title", "height", "data", "options", "scrollable"],
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
  },
  data() {
    return {
      resizeTimer: null,
    };
  },
  methods: {
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.$refs.seamlessScrollRef) {
          this.$refs.seamlessScrollRef.reset();
        }
      }, 200);
    },
  },
};
</script>

<style scope>
.event-box-header {
  position: relative;
  height: 40px;
  margin-bottom: 16px;
}
.event-box-toolbar {
  position: absolute;
  z-index: 2;
  right: 0;
  top: 0;
}

.flex {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.flex-row {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.flex-item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow: hidden;
}
</style>
