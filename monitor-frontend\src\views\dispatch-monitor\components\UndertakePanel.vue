<template>
  <div class="undertake-panel y-container no-padding">
    <div class="undertake-panel_global">
      <div class="undertake-panel_global_item" v-for="item in globalData" :key="item.name">
        <div class="stick">
          <div class="top"></div>
          <div class="bottom"></div>
        </div>
        <div class="info">
          <div class="label">{{ item.label }}</div>
          <div class="count">{{ data.totalStats[item.name] }}</div>
        </div>
      </div>
    </div>
    <!-- 自定义表头 -->
    <div class="table-header">
      <div class="header-cell" style="min-width: 60%">分中心名</div>
      <div class="header-cell" style="min-width: 20%">在办量</div>
      <div class="header-cell" style="min-width: 20%">已完成量</div>
    </div>
    <template v-if="data.centerStats.length > 0">
      <SeamlessScroll ref="seamlessScrollRef" :data="data.centerStats" :classOption="scrollOptions" :scrollable="data.centerStats.length > 4" class="scroll-wrapper">
        <el-table :data="data.centerStats" fit stripe class="table-body" :show-header="false">
          <el-table-column prop="CENTER_NAME" min-width="60%">
            <template slot-scope="scope">
              <div class="undertake-panel_list_item">
                <el-image class="dataicon" :src="require('@/assets/images/undertake-data-icon.png')"></el-image>
                <div class="text">{{ scope.row.CENTER_NAME }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="IN_PROGRESS_TASKS" min-width="20%">
            <template slot-scope="scope">
              <div class="color1">
                {{ scope.row.IN_PROGRESS_TASKS }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="COMPLETED_TASKS" min-width="20%">
            <template slot-scope="scope">
              <div class="color2">
                {{ scope.row.COMPLETED_TASKS }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </SeamlessScroll>
    </template>
  </div>
</template>

<script>
import { initRoll, cancelRoll } from "@/utils/table-roll";
// 假设 SeamlessScroll 已经全局注册，如果没有需要引入
// import SeamlessScroll from 'your-seamless-scroll-component-path';

export default {
  name: "UndertakePanel",
  props: {
    data: {
      type: Object,
      default: () => ({
        totalStats: {},
        centerStats: [],
      }),
    },
  },
  data() {
    return {
      resizeTimer: null,
      scrollOptions: {
        limitMoveNum: 2,
        step: 0.15,
        waitTime: 1000,
        hoverStop: true,
      },
      globalData: [
        { label: "任务总量", name: "TOTAL_TASKS_SUM" },
        { label: "在办总量", name: "IN_PROGRESS_TASKS_SUM" },
        { label: "已完成总量", name: "COMPLETED_TASKS_SUM" },
        { label: "平均处理时长(分钟)", name: "AVG_TASK_DURATION_SUM" },
      ],
    };
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
  },
  methods: {
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.$refs.seamlessScrollRef) {
          this.$refs.seamlessScrollRef.reset();
        }
      }, 200);
    },
  },
};
</script>

<style lang="scss" scoped>
.undertake-panel {
  flex: 1 0 288px;
  margin-top: 16px;

  .undertake-panel_global {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1.5fr;
    margin-bottom: 16px;
    width: 100%;

    .undertake-panel_global_item {
      @include flex-row;
      justify-content: flex-start;

      .stick {
        @include flex-col;
        gap: 6px;
        margin-right: 8px;
        .top {
          width: 2px;
          height: 16px;
          background: #99f3f9;
          box-shadow: 0px 0px 6px 0px rgba(169, 241, 246, 0.6);
        }

        .bottom {
          width: 2px;
          height: 38px;
          background: #ffffff;
          box-shadow: 0px 0px 6px 0px rgba(255, 255, 255, 0.6);
        }
      }

      .info {
        @include flex-col;
        align-items: flex-start;

        .label {
          font-size: 14px;
          color: $txtColor-reverse;
          margin-bottom: 4px;
        }

        .count {
          font-size: 20px;
          font-weight: bold;
          color: #00ffff;
        }
      }
    }
  }

  .undertake-panel_list_item {
    @include flex-row;
    justify-content: flex-start;
    gap: 16px;
    flex-shrink: 0;
    .dataicon {
      width: 32px;
      height: 32px;
    }
    .text {
      font-size: 14px;
      color: #fff;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      flex: 1;
    }
  }
}

/* 从 TypeBlocktable.vue 复制的样式 */
.table-header {
  display: flex;
  width: 100%;
  background: #0080ff30;
  height: 40px;
  line-height: 40px;
  margin-bottom: 8px;
  border-radius: 2px;
  padding: 0;

  .header-cell {
    flex: 1;
    min-width: 25%;
    color: #00aaff;
    font-size: 14px;
    text-align: left;
    padding: 0 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 16px;
  }
}

.scroll-wrapper {
  height: 160px;
  overflow: hidden;
  width: 100%;
  :deep(.el-table) {
    margin-top: 0 !important;
  }
}

.table-body {
  width: 100%;
  background-color: transparent;

  :deep(.el-table__body-wrapper) {
    overflow: visible;
  }

  :deep(.el-table__body) {
    border: none;
  }

  :deep(.el-table__row) {
    background-color: transparent !important;
    height: 40px;
  }

  :deep(td) {
    border: none;
    padding: 0;
    height: 40px;
    line-height: 40px;
  }

  :deep(.el-scrollbar__wrap) {
    overflow: visible !important;
  }

  :deep(.el-scrollbar__view) {
    position: static !important;
  }
}

.color1 {
  font-size: 16px;
  font-weight: bold;
  color: #00aaff;
}

.color2 {
  font-size: 16px;
  font-weight: bold;
  color: #00ffff;
}

.text {
  font-size: 16px;
  color: #fff;
}

.mt8 {
  margin-top: 16px;
}

:deep(.el-table) {
  &::before {
    display: none;
  }
}
</style>
