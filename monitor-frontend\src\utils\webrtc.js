import eventBus from "@/utils/eventbus.js";


let server,videohandel;
let isInit = false;
function init(config){
    config = config || window.webrtcJson;
    server = new window.yqWebrtcApp.Server(config);
    console.log('初始化视频对象')
    videohandel = server.createNetVideoChat({
        autoAccept: true
    });

    videohandel.onConnect(function(e){
        console.log('视频外呼创建链接>>>', e);
        updateVide(e.msgData.list)
        eventBus.$emit('webrtcConnect',e.msgData.list)
    })

    videohandel.onHangup(function(e){
        eventBus.$emit('webrtcHangup')
    })
    

    server.start()
}

function makecall(num,callback){
    if(location.protocol!='https:'){console.log('not support webrtc')}
    console.log('开始外呼>>>', num);
    videohandel.call && videohandel.call(num)
    callback && callback()
}

function clearCall(){
    videohandel.hangup()

}

function destroy(){
    server.close()
}

function updateVide(list) {
    list.forEach(function(item) {

        var tVideo = document.createElement('video');
        tVideo.srcObject = item.stream;
        tVideo.autoplay = true;
        tVideo.playsinline = 'playsinline';
        tVideo.muted = item.type == 'local' //||item.type=='remote'&&ctx.speakerMuted;
        tVideo.oncanplay = function() {
            this.play();
        };

        if (item.type == 'remote') {
            window.$('[data-ccbar-webrtc-video="remote"]').html(tVideo);
        } else {
            window.$('[data-ccbar-webrtc-video="local"]').html(tVideo);
        }
    })

}

export default{
    init,
    makecall,
    clearCall,
    destroy
}