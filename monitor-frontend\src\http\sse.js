import axios from 'axios'

const connections = new Map()

export default class SSEClient {
  constructor(clientId, options = {}) {
    const { 
      baseUrl = '/jr-rescuenet/api/sse/conn/', 
      url, 
      maxRetries = 3, 
      withCredentials = true, 
      msgHandler,
      enableSSE = true,        // 是否启用SSE
      pollInterval = 10000,     // 轮询间隔时间
      pollConfig = {},         // axios轮询请求配置
    } = options

    this.clientId = clientId
    this.url = url || `${baseUrl}${clientId}`
    this.options = {
      maxRetries,
      withCredentials,
      enableSSE,
      pollInterval,
      pollConfig,
      ...options
    }
    this.eventSource = null
    this.listeners = new Map()
    this.retryCount = 0
    this.polling = false
    this.pollTimer = null
  }

  connect() {
    if (this.options.enableSSE && typeof EventSource !== 'undefined') {
      this.connectSSE()
    } else {
      this.startPolling()
    }
  }

  connectSSE() {
    try {
      if (connections.has(this.url)) {
        console.warn(`[sse-connect-exists][${this.clientId}]:`, this.url)
        this.eventSource = connections.get(this.url).eventSource
        return
      }

      this.eventSource = new EventSource(this.url, {
        withCredentials: this.options.withCredentials
      })

      connections.set(this.url, this)

      this.on('error', (e) => {
        console.error(`[sse-error][${this.clientId}]:`, e)
        this.reconnect()
      })

      this.on('open', (e) => {
        console.log(`[sse-open][${this.clientId}]:`, e)
        this.retryCount = 0
      })

      this.on('message', (e) => {
        console.log(`[sse-message][${this.clientId}]:`, e)
        this.handleMessage(e.data)
      })
    } catch (error) {
      console.error(`[sse-init-error][${this.clientId}]:`, error)
      this.reconnect()
    }
  }

  startPolling() {
    if (this.polling) return
    
    this.polling = true
    console.log(`[polling-start][${this.clientId}]:`, this.url)
    
    const doPoll = async () => {
      try {
        const response = await axios({
          url: this.url,
          method: 'get',
          withCredentials: this.options.withCredentials,
          ...this.options.pollConfig
        })
        this.handleMessage(response.data)
      } catch (error) {
        console.error(`[polling-error][${this.clientId}]:`, error)
      }
      
      if (this.polling) {
        this.pollTimer = setTimeout(doPoll, this.options.pollInterval)
      }
    }

    doPoll()
  }

  handleMessage(data) {
    try {
      let parsedData = data
      if (typeof data === 'string') {
        parsedData = JSON.parse(data)
      }
      if (typeof this.options.msgHandler === 'function') {
        this.options.msgHandler(parsedData)
      }
    } catch (err) {
      console.error(`[message-parse-error][${this.clientId}]:`, err)
    }
  }

  close() {
    if (this.eventSource) {
      console.log(`[sse-close][${this.clientId}]:`, this.url)
      this.listeners.forEach(listener => {
        this.off(listener)
      })
      this.eventSource.close()
      this.eventSource = null
      connections.delete(this.url)
      axios.get(this.url.replace('conn', 'close'))
    }
    
    if (this.polling) {
      console.log(`[polling-stop][${this.clientId}]`)
      clearTimeout(this.pollTimer)
      this.polling = false
      this.pollTimer = null
    }
  }

  reconnect() {
    this.close()
    if (this.retryCount < this.options.maxRetries) {
      console.log(`[reconnect][${this.clientId}]:`, this.retryCount)
      this.retryCount++
      this.connect()
    }
  }

  // SSE专用方法，轮询模式下这些方法不会产生效果
  on(event, listener) {
    if (!this.eventSource) return
    
    if (this.listeners.has(event)) {
      this.off(event)
    }
    this.eventSource.addEventListener(event, listener)
    this.listeners.set(event, listener)
  }

  off(event) {
    if (!this.eventSource) return
    
    if (this.listeners.has(event)) {
      this.eventSource.removeEventListener(event, this.listeners.get(event))
      this.listeners.delete(event)
    }
  }
}