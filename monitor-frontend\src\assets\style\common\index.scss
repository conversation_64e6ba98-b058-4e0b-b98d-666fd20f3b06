@import '../../fonts/font.css';

// 变量
@import './variables.module.scss';
// 混入
@import './mixin.scss';
// y系列公共样式
@import './preset-style.scss';
// Element样式
@import './element-variables.scss';
// Element覆盖样式
@import './resetElement.scss';

// 全局公共样式
[v-cloak] {
  display: none !important;
}

* {
  font-family: 'PuHui';
  box-sizing: border-box;
  margin: 0px;
  padding: 0px;
}

html {
  font-size: 10px;
  line-height: 1.5;
  height: 100%;
}

body {
  position: relative;
  height: 100%;
  font-size: 1.4rem;
  // font-family: Alibaba PuHuiTi 3.0;
  // font-family: Arial, Helvetica, sans-serif;
  color: $txtColor;
  background-color: #000;
}

a {
  text-decoration: none;
}

/* h1,h2,h3,h4,h5,h6{font-weight: 400;} */
hr {
  background-color: #ddd;
  height: 1px;
  border: none;
}

input,
select {
  outline: 0;
}

ul {
  list-style: none;
}

#app {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 50% 50%;
  translate: -50% -50%;
  // width: $base_width;
  height: $base_height;
  overflow: hidden;
  width: 100vw;
  // height: 28vw;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: $bgColor-dark;
}

::-webkit-scrollbar-thumb:hover {
  background-color: $bgColor-dark--hover;
}

// @supports (scrollbar-width: thin) {
//   * {
//     scrollbar-width: thin;
//     scrollbar-color: #E8E8E8 transparent;
//   }
// }

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}

.blaze-text {
  background: linear-gradient(180deg, #FFFFFF 8%, #AFFAFF 63%, #9AE9FF 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}


// 过渡样式
/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.fade-in-left-enter-active,
.fade-in-left-leave-active,
.fade-in-right-enter-active,
.fade-in-right-leave-active {
  transition: all .5s ease-out;
}

.fade-in-left-enter,
.fade-in-right-leave-to {
  opacity: 0;
  transform: translateX(-50%);
}

.fade-in-right-enter,
.fade-in-left-leave-to {
  opacity: 0;
  transform: translateX(50%);
}