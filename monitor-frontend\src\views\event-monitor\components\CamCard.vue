<template>
  <div class="cam-box" @click="$emit('click')">
    <div class="cam-box-img">
      <img :src="imgSrc" style="width: 100%; height: 100%" />
    </div>
    <el-row type="flex" class="cam-box-detail" justify="space-between">
      <div :type="data.eqType" class="cam-box-type">
        <img :src="iconSrc" width="20" />
      </div>
      <el-col class="cam-box-name" :title="eqName">{{ eqName }}</el-col>
      <div
        class="cam-box-icon"
        style="color: #5d8c8f; font-size: 20px; cursor: pointer"
        @click.stop="$emit('remove', data)"
      >
        <i class="el-icon-remove-outline"></i>
      </div>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "CamCard",
  props: {
    data: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    startIndex: {
      type: Number,
      default: 1,
    },
  },
  computed: {
    groupedData() {
      return {
        0: "sxt", // 摄像头
        1: "zhidui", // 分队
        2: "wrj", // 无人机
        3: "zhongduan", // 手持设备终端
      };
    },
    imgSrc() {
      return `images/pv/${this.index + this.startIndex}.png`;
    },
    iconSrc() {
      return `images/svg-${this.groupedData[this.data.eqType] || "sxt"}.svg`;
    },
    eqName() {
      if (this.groupedData[this.data.eqType] === "sxt") {
        return "摄像头：" + this.data.eqName;
      } else if (this.groupedData[this.data.eqType] === "wrj") {
        return "无人机：" + this.data.eqName;
      } else if (this.groupedData[this.data.eqType] === "zhongduan") {
        return "手持终端：" + this.data.eqName;
      } else if (this.groupedData[this.data.eqType] === "zhidui") {
        return "分队：" + this.data.eqName;
      } else {
        return "";
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.cam-box {
  display: inline-block;
  vertical-align: top;
  width: 208px;
  color: #fff;
  border-radius: 4px;
  margin-bottom: 10px;
  opacity: 1;
  background: linear-gradient(
    180deg,
    rgba(99, 184, 190, 0.2) 0%,
    rgba(99, 184, 190, 0.05) 100%
  );

  .cam-box-img {
    height: 99px;
    margin: 8px;
    background-color: #fff;
    border-radius: 2px;
  }

  .cam-box-detail {
    line-height: 20px;
  }

  .cam-box-type {
    padding: 0 5px;
  }

  .cam-box-name {
    padding: 0 5px;
    flex: 1;
  }

  .cam-box-icon {
    padding: 0 5px;
    color: #5d8c8f;
    font-size: 20px;
  }
}
</style>
