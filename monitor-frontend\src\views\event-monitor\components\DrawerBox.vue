<template>
    <div class="drawer-box y-container">
        <div :class="[`drawer-box_header_${headerType}`, 'y-header']">
            <div :class="[`y-title_${headerType}`]">{{ title }}</div>

            <div class="actions">
                <slot name="right-header"></slot>
                <img style="cursor: pointer; margin-left: 6px" src="../images/closeButton.png" @click="close" />
            </div>
        </div>
        <div class="drawer-box_content">
            <slot></slot>
        </div>
        <div v-if="$slots.footer" class="drawer-box_footer">
            <slot name="footer"></slot>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "详情",
        },
        headerType: {
            type: Number,
            default: 1,
            validator: (value) => [1, 2].includes(value),
        },
    },
    methods: {
        close() {
            this.$emit("update:visible", false);
            this.$emit("close");
        },
    },
};
</script>

<style scoped lang="scss">
.drawer-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 400px;
    height: 100%;
    background: $themeColor;
    box-sizing: border-box;
    border-left: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow: -10px 0px 20px 0px #001112;
    z-index: 9999;

    .drawer-box_header_1 {
        @include flex-row;
        padding: 10px 10px 16px 32px;
        border: none;
        background: url("@/assets/images/event-detail-title-bg.png") no-repeat -16px center/contain;
    }

    .drawer-box_header_2 {
        position: relative;
        padding: 10px 10px 16px 32px;
        border: none;
        display: flex;
        justify-content: center;
        align-items: center;

        .actions {
            position: absolute;
            right: 10px;
        }
    }

    .drawer-box_content {
        position: relative;
        width: 100%;
        height: calc(100% - 90px);
        overflow: auto;
        flex: 1;
    }

    .drawer-box_footer {
        width: 100%;
        padding: 23px 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}

.y-title_1 {
    font-size: 20px;
    font-weight: bold;
    line-height: 30px;
    letter-spacing: 0px;
    background: linear-gradient(180deg, #ffffff 27%, #00aaff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.y-title_2 {
    width: 160px;
    line-height: 40px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    font-size: 20px;
    background: linear-gradient(270deg,
            rgba(0, 255, 255, 0) 0%,
            #01ffff 49%,
            rgba(0, 255, 255, 0) 100%);
    color: #ffffff !important;
}

.actions {
    display: flex;
    justify-content: flex-end;
}

.close {
    position: relative;
    top: -10px;
    right: -10px;
    font-size: 40px;
    color: $txtColor-light;
    cursor: pointer;
}

.el-button {
    font-size: 16px;
}
</style>