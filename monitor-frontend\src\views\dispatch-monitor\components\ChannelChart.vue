<template>
  <div class="channel-chart chart-container" ref="chart-container"></div>
</template>

<script>
import chartMixins from "@/mixins/chartMixins.js";
import { debounce } from "@/utils";
export default {
  name: "ChannelChart",
  mixins: [chartMixins],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  computed: {
    labelList() {
      return this.data.map((i) => i.NAME);
    },
    valueList() {
      return this.data.map((i) => i.COUNT);
    },
    option() {
      return {
        grid: {
          top: "25%",
          bottom: "0%",
          left: "5%",
          right: "2%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.labelList,
          axisLabel: {
            fontSize: this.setFontSize(14),
            color: "#fff",
            lineHeight: 24,
            interval: "auto",
          },
          axisTick: {
            show: true,
            alignWithLabel: true,
            length: 3,
          },
          axisLine: {
            show: true,
            color: "#254896",
          },
          z: 10,
        },
        yAxis: {
          type: "value",
          name: "数量",
          nameLocation: "end",
          nameGap: 15,
          nameTextStyle: {
            fontSize: 12,
            color: "#ffffff",
            padding: [0, 40, 0, 0],
          },
          minInterval: 1,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#ffffff",
            interval: "auto",
          },
          splitLine: {
            lineStyle: {
              color: "#283e53",
              type: [3, 5],
              dashOffset: 5,
            },
          },
        },
        tooltip: {
          confine: true,
          trigger: "axis",
          axisPointer: {
            lineStyle: {
              color: "#04CBD8",
            },
          },
          textStyle: {
            fontSize: this.setFontSize(12),
          },
        },
        series: [
          {
            type: "pictorialBar",
            data: this.valueList,
            symbol: "path://M12.000,-0.000 C12.000,-0.000 16.074,60.121 22.731,60.121 C26.173,60.121 -3.234,60.121 0.511,60.121 C7.072,60.121 12.000,-0.000 12.000,-0.000 Z",
            barWidth: 64,
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(4, 203, 216, 0.7)",
                  },
                  {
                    offset: 1,
                    color: "rgba(4, 203, 216, 0)",
                  },
                ],
                globalCoord: false,
              },
              borderWidth: 14,
              borderColor: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#04CBD8",
                  },
                  {
                    offset: 1,
                    color: "rgba(4, 203, 216, 0)",
                  },
                ],
                globalCoord: false,
              },
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
              fontSize: this.setFontSize(16),
              color: "#00FFFF",
            },
          },
        ],
      };
    },
  },
  methods: {
    setFontSize: debounce(function (res) {
      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      let fontSize = clientWidth / 1920;
      return res * fontSize;
    }, 500),
  },
  mounted() {
    window.addEventListener("resize", this.setFontSize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setFontSize);
  },
};
</script>

<style lang="scss" scoped>
.channel-chart {
  width: 424px;
  height: 214px;
  margin-top: 16px;
  margin-bottom: 24px;
}
</style>
