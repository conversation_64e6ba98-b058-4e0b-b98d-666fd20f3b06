<template>
  <div class="card-box">
    <div>
      <div class="card-box-title blaze-text"><img style="vertical-align: middle" src="../images/icon-inf.png" alt="" />{{ item.taskTitle }}</div>
      <div class="card-box-tags"></div>
    </div>

    <div class="card-box-detail">
      <table class="event-table">
        <tr>
          <th>发送时间：</th>
          <td>{{ item.logTime }}</td>
          <th>发送人：</th>
          <td>{{ item.logUser }}</td>
        </tr>
        <tr>
          <th>上报文字：</th>
          <td colspan="3">{{ item.logMemo }}</td>
        </tr>
        <tr>
          <th>图片视频：</th>
          <td colspan="3">
            <div v-if="item.logFile" @click="showImgSrc(item.logFile)" class="event-video">
              <img :src="pvImg(item.logFile)" alt="" />
            </div>
            <div v-if="item.logVideoFile" @click="showVideoSrc(item.logVideoFile)" class="event-video">
              <i style="font-size: 32px" class="el-icon-video-play"></i>
            </div>
          </td>
        </tr>
      </table>
    </div>

    <BaseDialog v-if="showVideo" :visible.sync="showVideo" title="视频" customStyle="width: 1158px; height: 780px;">
      <video controls :src="currentVideoSrc" style="height: 100%; width: 100%"></video>
    </BaseDialog>
  </div>
</template>

<script>
export default {
  props: ["item"],
  data() {
    return {
      currentVideoSrc: "",
      showVideo: false,
    };
  },
  methods: {
    showImgSrc(id) {
      let img = "/jr-rescuenet/api/file/preview/" + id;
      window.layer.photos({
        photos: {
          title: "现场图片",
          start: 0,
          data: [
            {
              alt: "图片",
              pid: 5,
              src: img,
            },
          ],
        },
        footer: false, // 是否显示底部栏 --- 2.8.16+
      });
    },
    showVideoSrc(id) {
      let src = "/jr-rescuenet/api/file/preview/" + id;
      this.currentVideoSrc = src;
      this.showVideo = true;
    },
    pvImg(id) {
      //图片
      return "/jr-rescuenet/api/file/preview/" + id;
    },
  },
};
</script>

<style lang="scss">
.card-box {
  margin-bottom: 16px;
  width: 100%;
  min-height: 170px;
  padding: 16px 30px 16px 16px;
  background: linear-gradient(270deg, rgba(4, 203, 216, 0.05) 0%, rgba(4, 203, 216, 0.1) 100%);

  .card-box-img {
    margin-right: 14px;
  }
  .card-box-title {
    font-size: 18px;
  }
  .card-box-tags {
    .tag {
      position: relative;
      background: rgba(4, 203, 216, 0.1);
      line-height: 24px;
      padding: 0 7px;
      margin-right: 8px;
      &:after {
        right: 0;
        content: "";
        position: absolute;
        width: 2px;
        height: 12px;
        background: linear-gradient(rgba(255, 255, 255, 1), rgba(4, 203, 216, 1));
        top: 50%;
        margin-top: -6px;
      }
      &:before {
        left: 0;
        content: "";
        position: absolute;
        width: 2px;
        height: 12px;
        background: linear-gradient(rgba(255, 255, 255, 1), rgba(4, 203, 216, 1));
        top: 50%;
        margin-top: -6px;
      }
    }
  }
  // .card-box-detail{}

  .event-video {
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #fff;
    text-align: center;
    vertical-align: middle;
    line-height: 40px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  th {
    color: #fff;
    font-size: 14px;
    opacity: 0.5;
    width: 70px;
    text-align: right;
  }

  td {
    color: #fff;
    font-size: 14px;
  }
}
.event-table {
  margin-top: 10px;
}
</style>
