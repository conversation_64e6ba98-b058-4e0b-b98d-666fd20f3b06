<template>
  <div class="missionCard-item">
    <div class="missionCard-item-leftPane">
      <div class="header">
        <img class="imgBox" src="../images/icon-yinji.png" alt="" />
        <div class="title blaze-text">
          {{ data.taskName }}
        </div>
      </div>

      <div class="detail">
        <table class="table">
          <tr>
            <th>所属机构</th>
            <td colspan="3">{{ data.teamName }}</td>
          </tr>
          <tr>
            <th>任务负责人</th>
            <td colspan="3">{{ data.leaderName }}</td>
          </tr>
          <tr>
            <th>目的地经纬度</th>
            <td colspan="3">
              {{ lonAndlat }}
            </td>
          </tr>
          <tr>
            <th>预计时间</th>
            <td colspan="3">{{ formatTime(data.expectedTime) }}</td>
          </tr>
        </table>
      </div>
    </div>

    <div class="missionCard-item-rightPane">
      <div class="top-box">
        <h3 class="distance">
          {{ formatDistance(data.kilometer) }}<span style="font-size: 14px">km</span>
        </h3>
        <span>当前里程</span>
      </div>
      <img
        style="cursor: pointer"
        src="../images/connectButton.png"
        @click="handleDetail(data)"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    lonAndlat() {
      return this.data.lonStr + "、" + this.data.latStr;
    },
  },
  methods: {
    handleDetail(data) {
      this.$emit("makecall", data);
    },
    formatTime(seconds) {
      if (!seconds) return "-";
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
      }
      return `${minutes}分钟`;
    },
    formatDistance(meters) {
      if (!meters) return '-';
      return (meters / 1000).toFixed(2);
    }
  },
};
</script>

<style lang="scss" scoped>
.missionCard-item {
  padding: 16px;
  overflow: auto;
  overflow: hidden;
  border-radius: 4px;
  background: rgba(0, 128, 255, 0.05);
  margin-bottom: 16px;

  display: flex;

  &-leftPane {
    flex: 1;

    .header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 0 16px 8px;

      .imgBox {
        width: 32px;
        height: 32px;
        margin-right: 14px;
        flex-shrink: 0;
      }

      .title {
        flex: 1;
        font-size: 18px;
        img {
          margin-right: 8px;
        }
      }
    }

    .detail {
      .table {
        width: 100%;
        font-size: 14px;
        color: #fff;

        th {
          color: #5d8c8f;
          width: 110px;
          padding: 4px;
          vertical-align: middle;
          text-align: left;

          &::after {
            content: ":";
          }
        }

        td {
          vertical-align: middle;
        }
      }

      .table-tag {
        display: inline-flex;
        align-items: center;
        position: relative;
        padding-right: 30px;
        font {
          margin-left: 8px;
        }
        &:after {
          content: "";
          display: inline-block;
          vertical-align: middle;
          position: absolute;
          right: 16px;
          top: 2px;
          opacity: 0.2;
          height: 16px;
          width: 1px;
          background: #04cbd8;
        }

        &:last-child {
          padding-right: 0;
          &:after {
            display: none;
          }
        }
      }
    }
  }

  &-rightPane {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .top-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);

      .distance {
        color: #ffdc00;
        font-size: 24px;
        font-weight: bold;
      }
    }
  }
}

.blaze-text {
  font-size: 24px;
  line-height: 36px;
}

.blaze-text2 {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  background: linear-gradient(180deg, #ffffff 8%, #ffe4aa 62%, #fbac06 95%);
}
</style>
