<template>
  <div class="important-event-list y-container no-padding">
    <div class="important-event-list_btns">
      <div class="important-event-list_btn" v-for="item in btnList" :key="item.name" :class="{ active: activeItem === item.name }" @click="activeItem = item.name">
        {{ item.label }}
      </div>
    </div>
    <div class="y-container no-padding">
      <template v-if="tableData.length > 0">
        <SeamlessScroll ref="seamlessScrollRef" style="width: 100%" :data="tableData" :classOption="scrollOptions" :scrollable="true">
          <div class="important-event-list_item" v-for="item in tableData" @click="handleDetail(item)" :key="item.disasterEventId">
            <el-image :src="require('@/assets/images/important-event-list-icon.png')" class="fleximg"> </el-image>
            <div style="flex: 1; display: flex; flex-direction: column" class="centerbox">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <div class="eventname">{{ item.disasterEventName }}</div>
                <div class="el-icon-arrow-right arrow"></div>
              </div>
              <div style="display: flex; align-items: center; justify-content: space-between">
                <div class="textword">{{ item.disasterEventCode }}</div>
                <div class="textword">{{ item.disasterTime }}</div>
              </div>
            </div>
            <!-- <div class="important-event-list_item_info" style="flex: 1; align-items: flex-start">
              <div class="important-event-list_item_info_title">{{ item.disasterEventName }}</div>
              <div class="text">{{ item.disasterEventCode }}</div>
            </div>
            <div class="important-event-list_item_info" style="align-items: flex-end">
              <div class="el-icon-arrow-right"></div>
              <div class="text">{{ item.disasterTime }}</div>
            </div> -->
          </div>
        </SeamlessScroll>
      </template>
      <el-empty v-else>
        <div slot="image"></div>
      </el-empty>
      <!-- <div class="important-event-list_item">
        <el-image :src="require('@/assets/images/important-event-list-icon.png')" class="fleximg"> </el-image>
        <div class="important-event-list_item_info" style="flex: 1; align-items: flex-start">
          <div class="important-event-list_item_info_title">{{ 11 }}</div>
          <div class="text">{{ 22 }}</div>
        </div>
        <div class="important-event-list_item_info" style="align-items: flex-end">
          <div class="el-icon-arrow-right"></div>
          <div class="text">{{ 33 }}</div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import { getEventList } from "@/api/event";
export default {
  components: {},
  inject: ["dateRange"],
  data() {
    return {
      activeItem: "4",
      btnList: [
        {
          label: "特大事件",
          name: "4",
        },
        {
          label: "重大事件",
          name: "3",
        },
      ],
      tableData: [],
      resizeTimer: null,
      scrollOptions: {
        limitMoveNum: 3,
        step: 0.15,
        waitTime: 1000,
        hoverStop: true,
      },
    };
  },
  computed: {},
  watch: {
    activeItem: {
      handler(val) {
        this.fetch();
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
  },
  methods: {
    async fetch() {
      const payload = {
        disasterLevel: this.activeItem,
        provinceCode: "410100",
        pageNum: 1,
        pageSize: 999,
        startTime: this.dateRange[0] + " 00:00:00",
        endTime: this.dateRange[1] + " 23:59:59",
      };
      const [err, res] = await getEventList(payload);
      if (err) return;
      this.tableData = res.data?.records || [];
    },
    handleDetail(row) {
      console.log(row);
      this.$router.push({
        path: "/event-monitor/" + row.disasterEventId,
      });
    },
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.$refs.seamlessScrollRef) {
          this.$refs.seamlessScrollRef.reset();
        }
      }, 200);
    },
  },
};
</script>

<style lang="scss" scoped>
.important-event-list {
  .important-event-list_btns {
    margin-top: 16px;
    @include flex-row;

    width: 100%;
    gap: 14px;
    cursor: pointer;

    .important-event-list_btn {
      @include flex-center;
      flex: 1;
      height: 48px;
      font-size: 18px;
      color: $txtColor-reverse;
      background: url("@/assets/images/important-event-list-bg.png") no-repeat center center;
      background-size: 100% 100%;

      &.active {
        background: url("@/assets/images/important-event-list-bg-active.png") no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }

  .important-event-list_item {
    // @include flex-row;
    display: flex;
    align-items: center;
    padding: 16px 0px 10px 16px;
    gap: 16px;
    width: 100%;
    cursor: pointer;
    .important-event-list_item_info {
      @include flex-col;
      height: 56px;
      color: #fff;
      margin-left: 16px;
    }

    .important-event-list_item_info_title {
      font-size: 16px;
      font-weight: bold;
    }
  }
}
.fleximg {
  width: 48px;
  height: 48px;
}
.text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}
.el-icon-arrow-right {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
}
.textword {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}
.centerbox {
  gap: 6px;
}
.arrow {
  font-size: 16px;
  color: #fff;
}
.eventname {
  font-size: 16px;
  color: #fff;
  font-weight: bold;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 280px;
}
</style>
