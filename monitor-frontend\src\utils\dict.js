import { getDict as getDictApi } from '@/api/dict'

// 获取字典（兼容单个和多个）
export async function getDict(dictNames) {
  function formatDict(dict) {
    return dict.reduce((acc, cur) => {
      acc[cur.dictValue] = cur.dictLabel || ''
      return acc
    }, {})
  }
  !Array.isArray(dictNames) && (dictNames = dictNames.split(','))
  const [err, res] = await getDictApi(dictNames)
  if (res && res.state == 1) {
    if (dictNames.length > 1) {
      return dictNames.reduce((acc, cur) => {
        acc[cur] = formatDict(res.data[cur])
        return acc
      }, {})
    } else {
      return formatDict(res.data)
    }
  }
}

// 获取字典并设置到data中
export function getAndSetDict(str, ctx = this) {
  let dictNames = str.split(',')
  return getDict(str).then(res => {
    if (dictNames.length > 1) {
      for (const key in res) {
        ctx[key] = res[key]
      }
    } else {
      ctx[dictNames[0]] = res
    }
  })
}