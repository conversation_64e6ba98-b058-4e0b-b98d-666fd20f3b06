<template>
  <div class="maincontent">
    <div class="mt8">
      <div class="table-header">
        <div class="header-cell">类型</div>
        <div class="header-cell">直接接报</div>
        <div class="header-cell">需调度接报</div>
        <div class="header-cell">接报量</div>
      </div>

      <template v-if="eventTypeStats.length > 0">
        <SeamlessScroll ref="seamlessScrollRef" :data="eventTypeStats" :classOption="scrollOptions" :scrollable="eventTypeStats.length > 4" class="scroll-wrapper">
          <el-table :data="eventTypeStats" fit stripe class="table-body" :show-header="false">
            <el-table-column prop="label" show-overflow-tooltip min-width="25%">
              <template slot-scope="scope">
                <div class="text">
                  {{ scope.row.label }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalCount" min-width="25%">
              <template slot-scope="scope">
                <div class="color1">
                  {{ scope.row.directCount }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalCount" min-width="25%">
              <template slot-scope="scope">
                <div class="color2">
                  {{ scope.row.dispatchCount }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="processCount" min-width="25%">
              <template slot-scope="scope">
                <div class="color3">
                  {{ scope.row.totalCount }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </SeamlessScroll>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "TypeBlocktable",
  props: {
    eventTypeStats: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      scrollOptions: {
        limitMoveNum: 2,
        step: 0.15,
        waitTime: 1000,
        hoverStop: true,
      },
      resizeTimer: null,
    };
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
  },
  methods: {
    handleResize() {
      // 清除之前的定时器
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      // 设置新的定时器，防抖处理
      this.resizeTimer = setTimeout(() => {
        if (this.$refs.seamlessScrollRef) {
          // 重新初始化滚动
          this.$refs.seamlessScrollRef.reset();
        }
      }, 200);
    },
  },
};
</script>

<style lang="scss" scoped>
.maincontent {
  width: 100%;
  // margin-bottom: 24px;
  margin-bottom: 12px;
}

.table-header {
  display: flex;
  width: 100%;
  background: #0080ff30;
  height: 40px;
  line-height: 40px;
  margin-bottom: 8px;
  border-radius: 2px;
  padding: 0;

  .header-cell {
    flex: 1;
    min-width: 25%;
    color: #00aaff;
    font-size: 14px;
    text-align: left;
    padding: 0 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 16px;
  }
}

.scroll-wrapper {
  height: 160px;
  overflow: hidden;

  :deep(.el-table) {
    margin-top: 0 !important;
  }
}

.table-body {
  width: 100%;
  background-color: transparent;

  :deep(.el-table__body-wrapper) {
    overflow: visible;
  }

  :deep(.el-table__body) {
    border: none;
  }

  :deep(.el-table__row) {
    background-color: transparent !important;
    height: 40px;
  }

  :deep(td) {
    border: none;
    padding: 0;
    height: 40px;
    line-height: 40px;
  }

  :deep(.el-scrollbar__wrap) {
    overflow: visible !important;
  }

  :deep(.el-scrollbar__view) {
    position: static !important;
  }
}

.color1 {
  font-size: 16px;
  font-weight: bold;
  color: #00aaff;
}

.color2 {
  font-size: 16px;
  font-weight: bold;
  color: #ffdc00;
}

.color3 {
  font-size: 16px;
  font-weight: bold;
  color: #00ffff;
}

.text {
  font-size: 14px;
  color: #fff;
}

.mt8 {
  margin-top: 16px;
}

:deep(.el-table) {
  &::before {
    display: none;
  }
}
</style>
