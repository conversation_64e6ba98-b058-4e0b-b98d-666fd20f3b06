<template>
  <div class="type-block">
    <div class="type-block_title">{{ data.label }}</div>
    <div class="type-block_content">
      <div class="type-block_content_item">
        <div class="label">接报量</div>
        <div class="total">{{ data.totalCount }}</div>
      </div>
      <div class="type-block_content_item">
        <div class="count primary">{{ data.directCount }}</div>
        <div class="count secondary">{{ data.dispatchCount }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.type-block {
  @include flex-row;
  position: relative;
  min-width: 258px;
  height: 86px;
  background: url('@/assets/images/type-block-bg.png') no-repeat center left/contain;

  .type-block_title {
    position: absolute;
    top: 0;
    left: 40px;
    transform: translateX(-50%);
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(180deg, #ffffff 54%, #b5d8f1 75%, #0555ce 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }

  .type-block_content {
    margin: 6px 0 6px 96px;

    .type-block_content_item {
      @include flex-row;
      width: 130px;
    }

    .label {
      font-size: 14px;
      background: linear-gradient(180deg, #ffffff 11%, #6487b8 82%, #0555ce 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }

    .total {
      font-size: 32px;
      font-weight: bold;
      background: linear-gradient(180deg, #ffffff 54%, #affaff 75%, #04cbd8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }

    .count {
      font-size: 24px;
      font-weight: bold;

      &::before {
        content: '';
        display: inline-block;
        margin: 14px 8px 14px 0;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        vertical-align: middle;
      }

      &.primary {
        background: linear-gradient(180deg, #ffffff 0%, #affaff 75%, #04cbd8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;

        &::before {
          background: #04cbd8;
        }
      }

      &.secondary {
        background: linear-gradient(180deg, #ffffff 0%, #ffe4aa 75%, #fbac06 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;

        &::before {
          background: #fbac06;
        }
      }
    }
  }
}
</style>
