let anime = null
let wrapperEl = null
let step = 1
let leastStep = 1
let frameCount = 0

const handleMouseenter = () => {
  rollStop()
}
const handleMouseleave = () => {
  rollStart(wrapperEl, wrapperEl.scrollHeight, wrapperEl.clientHeight)
}

export function initRoll(tableEl, options = {}) {
  if (!tableEl || !tableEl.$el) {
    return
  }
  const { speed = 1 } = options
  step = speed
  wrapperEl = tableEl.$el.querySelector('.el-table__body-wrapper')
  wrapperEl.addEventListener('mouseenter', handleMouseenter)
  wrapperEl.addEventListener('mouseleave', handleMouseleave)
  if (wrapperEl.scrollHeight === wrapperEl.clientHeight) {
    rollStop()
  } else {
    rollStart(wrapperEl, wrapperEl.scrollHeight, wrapperEl.clientHeight)
  }
}

export function cancelRoll() {
  if (wrapperEl) {
    wrapperEl.removeEventListener('mouseenter', handleMouseenter)
    wrapperEl.removeEventListener('mouseleave', handleMouseleave)
  }
  rollStop()
}

export function getLeastStep(scale) {
  if (!scale || scale <= 0) {
    return
  }
  leastStep = Math.max(1, scale)
}

function rollStart(el, scrollHeight, clientHeight, last = 0) {
  anime = requestAnimationFrame(() => {
    frameCount++
    // 每隔10帧才滚动一次，使滚动更慢
    if (frameCount % 10 === 0) {
      if (el.scrollTop >= scrollHeight - clientHeight) {
        el.scrollTop = 0
      } else {
        el.scrollTop += 1  // 固定使用1像素的步长
      }
      
      if (el.scrollTop === last) {
        el.scrollTop = 0
      } else {
        last = el.scrollTop
      }
    }
    
    rollStart(el, scrollHeight, clientHeight, last)
  })
}

function rollStop() {
  if (anime) {
    cancelAnimationFrame(anime)
    anime = null
  }
  frameCount = 0
}