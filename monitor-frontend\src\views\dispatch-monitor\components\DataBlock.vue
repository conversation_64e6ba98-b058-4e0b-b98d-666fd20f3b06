<template>
  <div class="data-block">
    <el-image
      style="width: 80px; height: 80px; margin-right: 24px"
      :src="icon"></el-image>
    <div :class="{ 'data-block_content': true, total }">
      <template v-if="total">
        <div class="title">{{ data.label }}</div>
        <div class="count">{{ data.count }}</div>
      </template>
      <template v-else>
        <div class="title">{{ data.label }}</div>
        <div class="item process-item">
          <div class="label">在办量</div>
          <div class="count">{{ data.processCount }}</div>
        </div>
        <div class="item total-item">
          <div class="label">已完成</div>
          <div class="count">{{ data.totalCount }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    icon: {
      type: String,
    },
    total: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.data-block {
  @include flex-row;
  align-items: flex-start;

  .data-block_content {
    @include flex-col;
    align-items: flex-start;

    .title {
      font-size: 20px;
      font-weight: bold;
      background: linear-gradient(180deg, #ffffff 0%, #f3d4c3 75%, #f2884a 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }

    .item {
      @include flex-row;
      gap: 24px;

      &.process-item {
        .label {
          font-size: 20px;
          background: linear-gradient(180deg, #ffffff 54%, #b5d8f1 75%, #0555ce 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }

        .count {
          font-size: 40px;
          font-weight: bold;
          line-height: 40px;
          background: linear-gradient(180deg, #ffffff 8%, #affaff 63%, #04cbd8 95%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
      }

      &.total-item {
        .label {
          font-size: 16px;
          color: #6487b8;
        }

        .count {
          font-size: 24px;
          font-weight: bold;
          line-height: 24px;
          background: linear-gradient(180deg, #9dc7f7 37%, #0773f0 95%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
      }
    }

    &.total {
      .title {
        font-size: 20px;
        font-weight: bold;
        background: linear-gradient(180deg, #ffffff 54%, #b5d8f1 75%, #0555ce 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }

      .count {
        font-size: 48px;
        font-weight: bold;
        background: linear-gradient(180deg, #ffffff 8%, #ffe4aa 62%, #fbac06 95%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }
}
</style>
