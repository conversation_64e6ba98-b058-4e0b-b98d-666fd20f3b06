import { getLeastStep } from './table-roll.js'

const documentWidth = document.documentElement.clientWidth

// 根据设计稿的宽高比例，动态调整页面的缩放比例
export function handleSreenScale(el) {
  const designDraftWidth = 3840 //设计稿的宽度
  const designDraftHeight = 1080 //设计稿的高度
  const standardScale = designDraftWidth / designDraftHeight
  const clientWidth = document.documentElement.clientWidth
  const clientHeight = document.documentElement.clientHeight
  const scale = clientWidth / clientHeight < standardScale ? clientWidth / designDraftWidth : clientHeight / designDraftHeight
  getLeastStep(scale)
  el.style.transform = `scale(${scale})`
}

// 用于计算echarts配置项的尺寸值
export function nowSize(val, initWidth = 1920) {
  return val * (documentWidth / initWidth);
}

export function handleSreenFontsize(screenRatioByDesign = 16 / 9) {
  let docEle = document.documentElement
  function setHtmlFontSize() {
    var screenRatio = docEle.clientWidth / docEle.clientHeight;
    var fontSize = (
      screenRatio > screenRatioByDesign
        ? (screenRatioByDesign / screenRatio)
        : 1
    ) * docEle.clientWidth / 10;
    docEle.style.fontSize = fontSize.toFixed(3) + "px";
    console.log(docEle.style.fontSize);
  }
  setHtmlFontSize()
  window.addEventListener('resize', setHtmlFontSize)
}