<template>
  <div id="app" ref="app">
    <router-view></router-view>
  </div>
</template>

<script>
import { debounce, handleSreenFontsize } from "@/utils";

export default {
  components: {},
  data() {
    return {};
  },
  computed: {},
  created() {
    // 视图过长时的比例缩放
    this.$nextTick(() => {
      // this.handleSreenScale();
      this.handleSreenFontsize()
      window.addEventListener("resize", this.resizeHandler);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeHandler);
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // this.$store.dispatch('user/remoteVideoConferenceCookie') // 视频会议相关已移除
    },
    handleSreenFontsize() {
      handleSreenFontsize()
    },
    // handleSreenScale() {
    //   handleSreenScale(this.$refs["app"]);
    // },
    resizeHandler: debounce(function () {
      // this.handleSreenScale();
      handleSreenFontsize()
    }, 500),
  },
};
</script>

<style lang="scss"></style>
