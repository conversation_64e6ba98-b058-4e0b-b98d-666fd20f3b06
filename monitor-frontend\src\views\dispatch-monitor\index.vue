<template>
  <Layout title="中移指挥调度大屏">
    <div class="monitor-container">
      <div class="left-panel y-container pdr">
        <BaseDelay delay="1100">
          <transition-group class="y-container no-padding" name="fade-in-right" appear>
            <Titlecomponent key="overview-title" title="今日全市情况总览" />
            <GlobalPanel key="global-panel" :data="overview" />
            <Titlecomponent key="channel-title" title="接报渠道情况" />
            <ChannelChart key="channel-chart" :data="phoneRegisterCount" />
            <Titlecomponent key="undertake-title" title="分中心承办情况" />
            <UndertakePanel key="undertake-panel" :data="taskOverview" />
          </transition-group>
        </BaseDelay>
      </div>

      <div class="center-panel y-container">
        <!-- <EventDataPanel :activeArea="activeArea" /> -->
        <BaseDelay delay="300">
          <transition name="area-appear" appear>
            <AreaChart @changeActive="handleChangeActive" />
          </transition>
        </BaseDelay>
      </div>

      <div class="right-panel y-container pdl">
        <BaseDelay delay="1100">
          <transition-group class="y-container no-padding" name="fade-in-left" appear>
            <!-- <EventList key="event-list" style="margin-top: 24px; margin-bottom: 40px" /> -->
            <Titlecomponent key="eventdataoverview" title="事件数据总览" />
            <DataBlocktable key="disasterEvent" :disasterEventStats="disasterEventStats" :totalEvents="totalEvents" :totalCalls="totalCalls" />
            <Titlecomponent key="reporttype" title="接报类型" />
            <TypeBlocktable key="typeevent" :eventTypeStats="eventTypeStats" />
            <Titlecomponent key="important-title" title="重点关注事件" />
            <ImportantEventList key="important-list" />
          </transition-group>
        </BaseDelay>
      </div>
    </div>
  </Layout>
</template>

<script>
import { computed } from "vue";
import dayjs from "dayjs";
import Layout from "@/layout";
import EventList from "./components/EventList";
import ImportantEventList from "./components/ImportantEventList";
import AreaChart from "./components/AreaChart";
import EventDataPanel from "./components/EventDataPanel";
import GlobalPanel from "./components/GlobalPanel";
import ChannelChart from "./components/ChannelChart";
import UndertakePanel from "./components/UndertakePanel";
import DataBlocktable from "./components/DataBlocktable.vue";
import TypeBlocktable from "./components/TypeBlocktable.vue";
import SSE from "@/http/sse";

import { getEventDataSummary } from "@/api/event";
import { getAndSetDict } from "@/utils";

export default {
  components: {
    Layout,
    // EventList,
    ImportantEventList,
    AreaChart,
    GlobalPanel,
    ChannelChart,
    UndertakePanel,
    //
    DataBlocktable,
    TypeBlocktable,
  },
  data() {
    return {
      dateRange: this.initDate(),
      activeArea: null,

      // 左侧数据
      sse: null,
      overview: {},
      phoneRegisterCount: [],
      taskOverview: {
        totalStats: {},
        centerStats: [],
      },
      //
      disasterEventStats: [],
      eventTypeStats: [],
      totalEvents: 0,
      totalCalls: 0,

      // dict
      RESCUE_EVENT: {},
      event_level: {},
    };
  },
  watch: {
    activeArea(newVal) {
      this.getEventDataSummary();
    },
  },
  provide() {
    return {
      dateRange: computed(() => this.dateRange),
    };
  },
  created() {
    // this.openStream();
    // getAndSetDict("RESCUE_EVENT,event_level", this);
    // this.getEventDataSummary();
  },
  beforeDestroy() {
    // this.closeStream();
  },
  methods: {
    //
    // async getEventDataSummary() {
    //   const payload = {
    //     areaCode: this.activeArea,
    //     startDate: this.dateRange[0].replaceAll(/-/g, ""),
    //     endDate: this.dateRange[1].replaceAll(/-/g, ""),
    //   };
    //   const [err, res] = await getEventDataSummary(payload);
    //   if (err) return;
    //   const { disasterEventStats, eventTypeStats, totalEvents, totalCalls } = res.data;
    //   this.disasterEventStats = (disasterEventStats || []).map((item) => ({
    //     ...item,
    //     label: this.event_level[item.disasterLevel],
    //     processCount: Number(item.totalCount - item.endCount) || 0,
    //     icon: require(`@/assets/images/level-${item.disasterLevel}-count.png`),
    //   }));
    //   this.disasterEventStats.sort((a, b) => b.disasterLevel - a.disasterLevel);
    //   this.eventTypeStats = (eventTypeStats || []).map((item) => ({
    //     ...item,
    //     label: this.RESCUE_EVENT[item.eventType],
    //     dispatchCount: Number(item.totalCount - item.directCount) || 0,
    //   }));
    //   this.totalEvents = totalEvents;
    //   this.totalCalls = totalCalls;
    // },
    //
    initDate() {
      let today = dayjs().format("YYYY-MM-DD");
      let monthAgo = dayjs().subtract(1, "month").format("YYYY-MM-DD");
      return [monthAgo, today];
    },
    // openStream() {
    //   if (this.sse) {
    //     return;
    //   }
    //   this.sse = new SSE("homeMonitor:" + this.dateRange.join("~").replaceAll(/-/g, ""), {
    //     enableSSE: false,
    //     url: "/jr-rescuenet/api/strategy/monitor/overview",
    //     pollConfig: {
    //       method: "post",
    //       data: {
    //         startDate: this.dateRange[0].replaceAll(/-/g, ""),
    //         endDate: this.dateRange[1].replaceAll(/-/g, ""),
    //       },
    //     },
    //     withCredentials: true,
    //     msgHandler: this.handleStreamData,
    //   });
    //   this.sse.connect();
    // },
    closeStream() {
      if (this.sse) {
        this.sse.close();
        this.sse = null;
      }
    },
    handleStreamData(res) {
      console.log("handleStreamData", res);
      if (res.data) {
        const { overview, phoneRegisterCount, taskOverview } = res.data;
        this.overview = overview || {};
        this.phoneRegisterCount = phoneRegisterCount || [];
        this.taskOverview = taskOverview || {
          totalStats: {},
          centerStats: [],
        };
      }
    },
    handleChangeActive(activeArea) {
      this.activeArea = activeArea;
    },
  },
};
</script>

<style lang="scss" scoped>
.monitor-container {
  @include full;
  position: relative;

  .center-panel {
    @include flex-center;
    position: relative;

    // .area-chart {
    //   position: absolute;
    //   top: 130px;
    //   left: 50%;
    //   transform: translateX(-50%);
    //   width: 1460px;
    //   height: 880px;
    // }

    .area-appear-enter-active,
    .area-appear-leave-active {
      transition: all 0.5s ease-out;
    }

    .area-appear-enter,
    .area-appear-leave-to {
      transform: translateX(-50%) scale(0);
    }
  }

  .left-panel {
    position: absolute;
    top: 0;
    left: 0;
    width: 520px;
    height: 980px;
    background: url("@/assets/images/left-panel-bg.png") no-repeat center center / calc(100% + 70px) calc(100% + 24px);
    z-index: 10;
  }

  .right-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 520px;
    height: 980px;
    background: url("@/assets/images/right-panel-bg.png") no-repeat center center / calc(100% + 70px) calc(100% + 24px);
    z-index: 10;
  }
  .pdl {
    padding-left: 72px;
    padding-top: 16px;
  }
  .pdr {
    padding-right: 72px;
    padding-top: 16px;
  }
}
</style>
