<template>
    <div class="maincontent">
        <div class="header">
            <h3 class="title">{{ title }}</h3>
            <el-button @click="handleButtonClick" class="btn">
                <span class="btntext">{{ buttonText }}</span>
            </el-button>
        </div>
        <div class="mt8">
            <el-table :data="tableData" height="100%" fit ref="table" stripe>
                <el-table-column prop="DEPT_NAME" label="单位" show-overflow-tooltip min-width="25%">
          <template slot-scope="scope">
            <div class="text">
              {{ scope.row.label }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="USERNAME" label="名称" min-width="25%">
          <template slot-scope="scope">
            <div class="color1">
              {{ scope.row.directCount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="MOBILE" label="通讯方式" min-width="25%">
          <template slot-scope="scope">
            <div class="color2">
              {{ scope.row.dispatchCount }}
            </div>
          </template>
        </el-table-column>
                <el-table-column label="操作" min-width="100">
                    <template slot-scope="scope">
                        <el-button @click="handleDelete(scope.$index)" type="danger" size="mini">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
export default {
    name: "VideoTable",
    props: {
        tableData: {
            type: Array,
            default: () => [],
        },
        title: {
            type: String,
            default: "Table Title",
        },
        buttonText: {
            type: String,
            default: "Button",
        },
    },
    methods: {
        handleButtonClick() {
            this.$emit("button-click");
        },
        handleDelete(index) {
            this.$emit("delete-row", index);
        },
    },
};
</script>

<style lang="scss" scoped>
.maincontent {
    width: 100%;
    margin-bottom: 24px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    height: 32px;
}

.title {
    font-family: Alibaba PuHuiTi 3.0;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    align-items: center;
    letter-spacing: 0px;
    color: #FFFFFF;
}

.btn {
    width: 112px;
    height: 32px;
    border-radius: 4px;
    opacity: 1;
    background: linear-gradient(180deg, rgba(0, 128, 255, 0.05) 0%, rgba(0, 128, 255, 0.5) 100%);
    box-sizing: border-box;
    border: 0px solid;
    border-image: linear-gradient(180deg, rgba(0, 128, 255, 0.2) 0%, #86C3FF 100%) 1;
    box-shadow: inset 0px 0px 10px 0px rgba(0, 128, 255, 0.5);
}

.btntext {
    font-family: Alibaba PuHuiTi 3.0;
    font-size: 16px;
    font-weight: normal;
    line-height: 32px;
    align-items: center;
    letter-spacing: 0px;
    color: #FFFFFF;
}

.mt8 {
    margin-top: 16px;
    height: 205px;
}
</style>
