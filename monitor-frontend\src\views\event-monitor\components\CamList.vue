<template>
  <div class="cam-list">
    <CamCard
      v-for="(item, index) in list"
      :key="index"
      :data="item"
      :index="index"
      :startIndex="startIndex"
      @click="$emit('click',item)"
      @remove="$emit('remove', item)"
    />
  </div>
</template>

<script>
import CamCard from './CamCard.vue'

export default {
  name: '<PERSON><PERSON><PERSON>',
  components: {
    CamCard
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    startIndex: {
      type: Number,
      default: 1
    }
  }
}
</script>

<style lang="scss" scoped>
.cam-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}
</style>
