<template>
  <transition-group name="base-dialog-fade" @after-enter="afterEnter" @after-leave="afterLeave">
    <div v-if="visible" class="base-dialog_mask" key="mask"></div>
    <div v-show="visible" key="dialog" class="base-dialog y-container" :style="customStyle">
      <div class="base-dialog_header y-header">
        <h3 class="y-title blaze-text1">
          {{ title }}
        </h3>
        <!-- <svg-icon @click.native="close" class="close" icon="fork"></svg-icon> -->
        <el-image @click.native="close" class="close" :src="require('@/assets/images/closed.png')"></el-image>
      </div>
      <div class="y-container no-padding">
        <slot></slot>
      </div>
    </div>
  </transition-group>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
    },
    customStyle: {
      type: [String, Object],
      default: null,
    },
  },
  data() {
    return {};
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        document.querySelector("#app").appendChild(this.$el);
      }
    },
  },
  computed: {},
  mounted() {
    if (this.visible) {
      document.querySelector("#app").appendChild(this.$el);
    }
  },
  destroyed() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
  methods: {
    afterEnter() {
      this.$emit("opened");
    },
    afterLeave() {
      this.$emit("closed");
    },
    close() {
      this.$emit("close");
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.base-dialog {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 9999;
  transform: translate(-50%, -50%);
  width: 1654px;
  height: 900px;
  border-radius: 16px;
  background: #001b57;
  box-sizing: border-box;
  border: 1px solid transparentize($themeColor, 0.2);
  box-shadow: 0px 0px 100px 0px #001112, inset 0px 0px 12px 0px transparentize($themeColor, 0.5);

  .base-dialog_header {
    padding: 10px 10px 16px 32px;
    border: none;
    background: url("@/assets/images/event-detail-title-bg.png") no-repeat -16px center/contain;

    .y-title {
      font-size: 20px;
      font-weight: bold;
    }

    .close {
      position: relative;
      top: -10px;
      right: -10px;
      z-index: 10;
      font-size: 40px;
      color: $txtColor-light;
      cursor: pointer;
      width: 40px;
      height: 40px;
    }
  }
}

.base-dialog_mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0);
}

.base-dialog-fade-enter-active {
  animation: dialog-fade-in 0.3s;
}

.base-dialog-fade-leave-active {
  animation: dialog-fade-out 0.3s;
}

@keyframes dialog-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes dialog-fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
</style>
