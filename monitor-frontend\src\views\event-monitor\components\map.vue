<template>
  <div class="map-wrapper">
    <div id="map-container" ref="mapcontainer"></div>
    <!-- 信息弹框 -->
    <MapInfoWindow
      v-if="activeMarker && showInfoWindow"
      @close="closeWindow"
      :item="lastItem"
      :marker="activeMarker"
      :position="infoWindowStyle"
      @toWall="handleToWall"
    />
    <!-- 过滤器 -->
    <div class="box-center2" type="center" :style="legendStyle">
      <ul>
        <li
          @click="toggleMarkerGroup(key)"
          :type="item.type"
          :class="{ slt: item.type == lastItem.eqType, active: item.active }"
          v-for="(item, key) in types"
          :key="key"
        >
          <img :src="`./images/svg-${item.icon}.svg`" alt="" />
          <span>{{ item.name }}</span>
        </li>
      </ul>
    </div>
    <!-- 操作按钮 -->
    <div class="map-ctrl-box" :style="controlStyle">
      <div @click="handleZoomIn" class="btn">
        <img src="../images/zoom.png" />
      </div>
      <div @click="handleZoomOut" class="btn">
        <img src="../images/zoom-in.png" />
      </div>
      <div @click="handleZoomCenter" class="btn">
        <img src="../images/zoom-center.png" />
      </div>
    </div>
    <!-- mask -->
    <div class="gradient-overlay" v-if="overlay"></div>
  </div>
</template>
<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import MapInfoWindow from "./infowindow.vue";

let AMap;
export default {
  name: "map-view",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    eventData: {
      type: Object,
      default: () => ({}),
    },
    overlay: {
      type: Boolean,
      default: true,
    },
    controlStyle: {
      type: Object,
      default: () => null,
    },
    legendStyle: {
      type: Object,
      default: () => null,
    },
  },
  components: { MapInfoWindow },
  data() {
    return {
      infoWindowStyle: {},
      activeMarker: null,
      showInfoWindow: false,
      lastItem: {},
      map: null,
      minZoom: 3, // 最小缩放级别
      maxZoom: 18, // 最大缩放级别
      markers: new Map(),
      markerGroups: new Map(), // 存储不同类型的图层组
      types: {
        event: { name: "事件地点", icon: "shijian", active: true },
        type0: { name: "摄像头", icon: "sxt2", type: 0, active: true },
        type1: { name: "行动支队", icon: "zhidui", type: 1, active: true },
        type2: { name: "无人机", icon: "wrj2", type: 2, active: true },
        type3: { name: "手持终端", icon: "shouchi", type: 3, active: true },
      },
    };
  },
  mounted() {
    this.initAMap();
  },
  unmounted() {
    this.map?.destroy();
  },
  watch: {
    eventData: {
      handler(newVal) {
        console.log("newVal", newVal);
      },
    },
  },
  methods: {
    closeWindow() {
      this.showInfoWindow = false;
    },
    // 初始化分组对象
    groupEquipmentByType(data) {
      const groupedData = {
        type0: [], // 摄像头
        type1: [], // 行动支队
        type2: [], // 无人机
        type3: [], // 手持设备终端
      };

      // 处理 aroundMgrList
      if (data.aroundMgrList) {
        data.aroundMgrList.forEach((item) => {
          if (typeof groupedData[`type${item.eqType}`] == "undefined") {
            groupedData[`type${item.eqType}`] = [];
          }
          groupedData[`type${item.eqType}`].push(item);
        });
      }

      // 处理 dynamicMgrList
      if (data.dynamicMgrList) {
        data.dynamicMgrList.forEach((item) => {
          if (item.eqType != 2) return;
          if (typeof groupedData[`type${item.eqType}`] == "undefined") {
            groupedData[`type${item.eqType}`] = [];
          }
          groupedData[`type${item.eqType}`].push(item);
        });
      }

      if (data.taskInfoList) {
        data.taskInfoList.forEach((item) => {
          // 为taskInfoList中的item设置固定的eqType为1
          item.eqType = 1;
          if (typeof groupedData[`type${item.eqType}`] == "undefined") {
            groupedData[`type${item.eqType}`] = [];
          }
          groupedData[`type${item.eqType}`].push({
            ...item,
            eqName: item.taskName,
            eqType: 1,
            id: item.taskId,
            eqLon: item.lon,
            eqLat: item.lat,
          });
        });
      }

      return groupedData;
    },
    creatMarkerGroup() {
      let objList = this.groupEquipmentByType(this.data);

      if (objList.type1 && objList.type1.length > 0) {
        objList.type1.forEach((item) => {
          this.createMapDriving(
            [item.eqLon, item.eqLat],
            [this.eventData.disasterLon, this.eventData.disasterLat]
          );
        });
      }

      Object.keys(objList).forEach((type) => {
        this.markerGroups.set(type, new AMap.LayerGroup());
      });
      for (let key in objList) {
        // if (key == "type1") {
        //   objList[key].forEach((item) => {
        //     this.createMapDriving(
        //       [item.eqLon, item.eqLat],
        //       [this.eventData.disasterLon, this.eventData.disasterLat]
        //     );
        //   });
        // }
        this.batchAddMarkers(objList[key], key);
      }
    },
    createMarker(item) {
      let _self = this;
      const marker = new AMap.Marker({
        position: new AMap.LngLat(item.eqLon, item.eqLat),
        title: item.eqName,
        content: `<div id="marker-${item.id}" type="${item.eqType}" class="map-marker2"></div>`,
        extData: item,
        draggable: false,
        offset: new AMap.Pixel(0, 0),
        anchor: "bottom-center",
        zIndex: 100,
      });

      marker.on("click", function (e) {
        _self.activeMarker = marker;
        _self.lastItem = item;
        _self.updateInfoWindowPosition(this.activeMarker);
        _self.showInfoWindow = true;

        //console.log('===>',position,pixel)
        // document.querySelectorAll('.map-marker2').forEach(m => {
        //   console.log('m',m)
        //   m && m.classList.remove('active');
        // });
        // marker.element && marker.element.classList.add('active');

        window.$(".map-marker2").removeClass("active");
        window.$("#marker-" + item.id).addClass("active");
      });
      //this.bindMarkerEvents(marker)
      return marker;
    },
    // 批量添加标记点
    batchAddMarkers(markerList, key) {
      const newMarkers = markerList.map((markerData, index) => {
        const marker = this.createMarker({ ...markerData, index });
        this.markers.set(markerData.id, marker);

        // 将marker添加到对应类型的图层组
        const type = key;
        const group = this.markerGroups.get(type);
        if (group) {
          group.addLayer(marker);
        }

        return marker;
      });

      // 确保所有图层组都添加到地图上
      this.markerGroups.forEach((group) => {
        group.setMap(this.map);
      });

      return newMarkers;
    },

    // 切换指定类型的标记点显示/隐藏
    toggleMarkerGroup(type) {
      const group = this.markerGroups.get(type);
      if (group) {
        let visible = !this.types[type].active;
        this.types[type].active = visible;
        if (visible) {
          group.show();
        } else {
          group.hide();
        }
      }
    },

    // 获取指定类型的所有标记点
    getMarkersByType(type) {
      return Array.from(this.markers.values()).filter(
        (marker) => marker.getExtData().type === type
      );
    },

    // 批量删除标记点时同时处理分组
    batchRemoveMarkers(markerIds) {
      const markersToRemove = [];

      markerIds.forEach((id) => {
        const marker = this.markers.get(id);
        if (marker) {
          const type = marker.getExtData().type || "normal";
          const group = this.markerGroups.get(type);
          if (group) {
            group.removeLayer(marker);
          }
          markersToRemove.push(marker);
          this.markers.delete(id);
        }
      });
    },

    // 清除所有标记点
    clearAllMarkers() {
      this.markers.clear();
      this.markerGroups.forEach((group) => {
        group.clearLayers();
      });
    },

    // 清除指定类型的所有标记点
    clearMarkersByType(type) {
      const group = this.markerGroups.get(type);
      if (group) {
        const markersToRemove = this.getMarkersByType(type);
        markersToRemove.forEach((marker) => {
          this.markers.delete(marker.getExtData().id);
        });
        group.clearLayers();
      }
    },
    updateInfoWindowPosition(e) {
      let _self = this;
      let marker = _self.activeMarker;
      const position = marker.getPosition();
      const pixel = _self.map.lngLatToContainer(position); // Marker 锚点 (底部中心) 的像素坐标

      // --- 动态计算 InfoWindow 位置 ---
      // 定义相关尺寸和间距
      const markerHeight = 90; // .map-marker2 的高度
      const markerWidth = 60; // .map-marker2 的宽度

      // 计算 Top: InfoWindow 底部在 Marker 顶部之上 padding 距离
      const infoWindowTop = pixel.y - markerHeight;

      // 计算 Left: InfoWindow 左边缘在 Marker 中心点左侧 20px (markerWidth * 0.2)
      const infoWindowLeft = pixel.x - markerWidth * 0.2;
      // --- 动态计算结束 ---

      _self.infoWindowStyle = {
        left: `${infoWindowLeft}`,
        top: `${infoWindowTop}`,
      };
    },
    addCenterPoint(eventData) {
      let point = new AMap.LngLat(eventData.disasterLon, eventData.disasterLat);

      let marker = new AMap.Marker({
        position: point,
        // 将 html 传给 content
        content: '<div class="map-marker"></div>',
        // 以 icon 的底部中心为原点
        offset: new AMap.Pixel(0, 0), // 通常 bottom-center 锚点配合 (0,0) 偏移
        anchor: "bottom-center", // 将锚点设置为底部中心
      });

      // 将 markers 添加到地图
      this.map.add(marker);
    },
    initAMap() {
      window._AMapSecurityConfig = {
        securityJsCode: "df17b293a5e65c3c8a752a3684333c3c",
      };
      AMapLoader.load({
        key: "ec8a3c7bf561b63ad990e62eae6837c2", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMapCB) => {
          AMap = AMapCB;
          this.map = new AMap.Map("map-container", {
            // 设置地图容器id
            viewMode: "2D", // 是否为3D地图模式
            zoom: 15, // 初始化地图级别
            mapStyle: "amap://styles/ee1d98edaf4df712e03379ab845db7e2",
            center: [this.eventData.disasterLon, this.eventData.disasterLat], // 初始化地图中心点位置
          });
          this.creatMarkerGroup();
          this.addCenterPoint(this.eventData);

          // 监听地图移动事件
          ["movestart", "mapmove", "moveend", "zoomstart", "zoomend"].forEach(
            (eventName) => {
              this.map.on(eventName, () => {
                if (this.activeMarker) {
                  this.updateInfoWindowPosition(this.activeMarker);
                }
              });
            }
          );
        })
        .catch((e) => {
          console.log(e);
        });
    },

    createMapDriving(startLngLat = [], endLngLat = []) {
      if (!AMap) return;

      AMap.plugin("AMap.Driving", () => {
        const driving = new AMap.Driving({
          map: this.map,
          policy: 0,
          hideMarkers: true,
          autoFitView: true,
          showTraffic: true,
        });

        driving.search(startLngLat, endLngLat, function (status, result) {
          //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
          //查询成功时，result 即为对应的驾车导航信息
          console.log("status: ", status);

          console.log("result: ", result);
        });
      });
    },

    // 放大
    handleZoomIn() {
      const zoom = this.map.getZoom();
      if (zoom < this.maxZoom) {
        this.map.setZoom(zoom + 1);
      }
    },

    // 缩小
    handleZoomOut() {
      const zoom = this.map.getZoom();
      if (zoom > this.minZoom) {
        this.map.setZoom(zoom - 1);
      }
    },
    handleZoomCenter() {
      let point = new AMap.LngLat(
        this.eventData.disasterLon,
        this.eventData.disasterLat
      );
      this.map.setCenter(point);
      this.map.setZoom(11);
    },
    handleToWall(data) {
      this.$emit("toWall", data);
    },
  },
};
</script>
<style lang="scss" scoped>
#map-container {
  width: 100%;
  height: 100%;
}
</style>

<style lang="scss">
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-marker {
  width: 200px;
  height: 235px;
  background: url(../images/marker-event.png) no-repeat center;
  background-size: auto 100%;
  position: relative;
  &:after {
    pointer-events: none;
    margin-left: -156px;
    margin-top: 81px;
    content: "";
    border-radius: 50%;
    position: absolute;
    width: 522px;
    height: 285px;
    background: rgba(249, 71, 66, 0.05);
    box-shadow: inset 0px 0px 32px 0px rgba(249, 71, 66, 0.2);
    animation: scaleUp 3s infinite;
    animation-timing-function: ease-in-out;
  }
  z-index: 3;
}
@keyframes scaleUp {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(2);
  }
}
.map-marker2 {
  width: 100px;
  height: 135px;
  position: relative;
  background-size: auto 100%;
  &[type="0"] {
    background: url(../images/marker-sxt.png) no-repeat center;
    &.active {
      z-index: 44;
      &:after {
        margin-top: -21px;
        margin-left: -23px;
        position: absolute;
        left: 14px;
        top: 14px;
        content: "";
        width: 120px;
        height: 120px;
        background: url(../images/marker-sxt-active.png) no-repeat center;
        background-size: auto 120px;
      }
    }
  }
  &[type="1"] {
    background: url(../images/marker-xdzd.png) no-repeat center;
    &.active {
      z-index: 44;
      &:after {
        margin-top: -21px;
        margin-left: -23px;
        position: absolute;
        left: 14px;
        top: 14px;
        content: "";
        width: 120px;
        height: 120px;
        background: url(../images/marker-xdzd-active.png) no-repeat center;
        background-size: auto 120px;
      }
    }
  }
  &[type="2"] {
    background: url(../images/marker-wrj.png) no-repeat center;
    &.active {
      z-index: 44;
      &:after {
        margin-top: -21px;
        margin-left: -23px;
        position: absolute;
        left: 14px;
        top: 14px;
        content: "";
        width: 120px;
        height: 120px;
        background: url(../images/marker-wrj-active.png) no-repeat center;
        background-size: auto 120px;
      }
    }
  }
  &[type="3"] {
    background: url(../images/marker-zhongduan.png) no-repeat center;
    &.active {
      z-index: 44;
      &:after {
        margin-top: -21px;
        margin-left: -23px;
        position: absolute;
        left: 14px;
        top: 14px;
        content: "";
        width: 120px;
        height: 120px;
        background: url(../images/marker-zhongduan-active.png) no-repeat center;
        background-size: auto 120px;
      }
    }
  }
}
.map-ctrl-box {
  z-index: 3;
  position: absolute;
  right: 590px;
  bottom: 80px;
  .btn {
    margin-bottom: 24px;
    opacity: 0.8;
    width: 40px;
    height: 40px;
    cursor: pointer;
    &:hover {
      opacity: 1;
    }
  }
}
.box-center2 {
  z-index: 3;
  position: absolute;
  left: 50%;
  display: inline-block;
  bottom: 24px;
  transform: translateX(-50%);
  ul {
    list-style: none;
    margin: 0 10px;
    padding: 16px 0;
    color: #fff;
    text-align: center;
    img {
      vertical-align: middle;
      margin-right: 8px;
    }
    li {
      display: inline-block;
      text-align: center;
      cursor: pointer;
      margin-right: 15px;
      width: 140px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      &[type="1"].active {
        background-color: rgba(240, 234, 12, 0.1);
        background: rgba(69, 245, 143, 0.1);
      }
      &[type="2"].active {
        background-color: rgba(240, 234, 12, 0.1);
        border-image: linear-gradient(
            0deg,
            #f0ea0c 0%,
            rgba(240, 234, 12, 0.3) 100%
          )
          1;
      }
      &[type="3"].active {
        border: 1px dashed;
        background-color: rgba(7, 115, 240, 0.1);
        border-image: linear-gradient(
            0deg,
            #0773f0 0%,
            rgba(7, 115, 240, 0.3) 98%
          )
          1;
      }

      &[type="0"].slt {
        background: radial-gradient(
          91% 91% at 50% 50%,
          rgba(69, 245, 143, 0.24) 0%,
          rgba(69, 245, 143, 0) 100%
        );
        box-sizing: border-box;
        border: 1px dashed;
        border-image: linear-gradient(
            0deg,
            #45f58f 0%,
            rgba(69, 245, 143, 0.3) 100%
          )
          1;
        box-shadow: inset 0px 0px 16px 0px rgba(69, 245, 143, 0.75);
      }

      &[type="2"].slt {
        background: radial-gradient(
          91% 91% at 50% 50%,
          rgba(240, 234, 12, 0.24) 0%,
          rgba(69, 245, 143, 0) 100%
        );
        box-sizing: border-box;
        border: 1px dashed;
        border-image: linear-gradient(
            0deg,
            #f0ea0c 0%,
            rgba(240, 234, 12, 0.3) 100%
          )
          1;
        box-shadow: inset 0px 0px 16px 0px rgba(240, 234, 12, 0.75);
      }

      &[type="3"].slt {
        background: radial-gradient(
          91% 91% at 50% 50%,
          rgba(7, 115, 240, 0.24) 0%,
          rgba(7, 115, 240, 0) 100%
        );
        box-sizing: border-box;
        border: 1px dashed;
        border-image: linear-gradient(
            0deg,
            #0773f0 0%,
            rgba(7, 115, 240, 0.3) 98%
          )
          1;
        box-shadow: inset 0px 0px 16px 0px rgba(7, 115, 240, 0.75);
      }
    }
  }
}
.gradient-overlay {
  z-index: 1;
  width: 100%;
  height: 100%;
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
}

.gradient-overlay::before,
.gradient-overlay::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 520px;
  pointer-events: none;
}

.gradient-overlay::before {
  left: 0;
  background: linear-gradient(
    to right,
    #061441 0%,
    #051e5ef8 85%,
    rgba(0, 0, 0, 0) 100%
  );
}

.gradient-overlay::after {
  right: 0;
  background: linear-gradient(
    to left,
    #061441 0%,
    #051e5e 85%,
    rgba(0, 0, 0, 0) 100%
  );
}
</style>
