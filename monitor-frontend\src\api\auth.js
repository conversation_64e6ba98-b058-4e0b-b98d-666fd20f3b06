import { get, post } from '@/http/request'

const PREFIX = '/jr-rescuenet/api/ucm'

/**
 * 获取当前登录用户信息
 * @returns {Promise} 返回用户信息请求的Promise对象
 */
export function getUserInfo() {
  return get('/userInfo')
}

/**
 * 获取云视讯视频会议 Cookie
 */
export function getVideoConferenceCookie() {
  return get(`${PREFIX}/loginUcm`)
}

/**
 * 视频会议心跳
 */
export function videoConferenceHeartbeat(cookie) {
  return get(`${PREFIX}/ping`, {
    cookie,
  })
}

/**
 * 创建云视讯视频会议
 */
export function createVideoConference(data) {
  return post(`${PREFIX}/createMeeting`, data)
}
