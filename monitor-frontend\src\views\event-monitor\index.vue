<template>
  <Layout title="应急指挥一张图">
    <div class="monitor-container">
      <div class="left-panel">
        <BaseDelay delay="800">
          <transition-group
            class="y-container no-padding"
            name="fade-in-right"
            appear
          >
            <EventBox title="事件简报" key="event-report-box">
              <div class="event-report">
                <div class="clearfix">
                  <div class="event-report-img">
                    <img
                      class="event-report-img"
                      src="./images/icon-report.png"
                      alt=""
                    />
                  </div>
                  <div class="event-report-title">
                    <span class="blaze-text">{{
                      disasterEventData.disasterEventName
                    }}</span>
                    <div class="event-report-time">
                      <span class="color2">发生时间:</span>
                      {{ disasterEventData.disasterTime }}
                    </div>
                  </div>
                </div>

                <div
                  class="event-report-detail"
                  style="background: rgba(112, 188, 193, 0.05)"
                >
                  <table class="event-table" style="">
                    <tr>
                      <th>事件类型</th>
                      <td>
                        <el-tag type="danger" size="small">{{
                          dict(disasterEventData.disasterType, "RESCUE_EVENT")
                        }}</el-tag>
                      </td>
                      <th>事件等级</th>
                      <td>
                        {{
                          dict(disasterEventData.disasterLevel, "event_level")
                        }}
                      </td>
                    </tr>
                    <tr>
                      <th>发生地点</th>
                      <td colspan="3">
                        {{ disasterEventData.disasterLocation }}
                      </td>
                    </tr>
                    <tr>
                      <th>事件描述</th>
                      <td colspan="3">
                        {{ disasterEventData.disasterEventDesc }}
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
            </EventBox>

            <div class="flex-row buttons" key="event-buttons-box">
              <div class="monitor-button" @click="showInfomation = true">
                全息情报
              </div>
              <div class="monitor-button" @click="showCommande = true">
                指挥参考
              </div>
              <div class="monitor-button" @click="showVideoConference = true">
                视频会议
              </div>
            </div>

            <div style="overflow: scroll" key="event-cam-box">
              <EventBox title="周边监控">
                <CamList
                  :list="monitorData.aroundMgrList"
                  :startIndex="1"
                  @click="makecallFull"
                  @remove="removeCamera"
                />
              </EventBox>

              <EventBox title="动态监控">
                <CamList
                  :list="monitorData.dynamicMgrList"
                  :startIndex="4"
                  @click="makecallFull"
                  @remove="removeCamera"
                />
              </EventBox>
            </div>
          </transition-group>
        </BaseDelay>
      </div>

      <div class="box-center-main">
        <Map
          v-if="
            isMapDataInit &&
            disasterEventDataInit &&
            monitorData &&
            disasterEventData
          "
          :data="monitorData"
          :eventData="disasterEventData"
          @toWall="handleToWall"
        ></Map>

        <div class="box-center-back" @click="backToList">
          <i class="el-icon-arrow-left"></i>
          <span>返回上一层</span>
        </div>
      </div>

      <div class="right-panel">
        <BaseDelay delay="800">
          <transition-group
            class="y-container no-padding"
            name="fade-in-left"
            appear
          >
            <EventBox
              title="调度任务"
              height="340px"
              key="task-box"
              :data="monitorData.taskInfoList || []"
              :options="scrollOptions"
              :scrollable="monitorData?.taskInfoList?.length > 1"
            >
              <MissionCardItem
                v-for="item in monitorData.taskInfoList || []"
                :data="item"
                :key="item.taskId"
                @makecall="makecall"
              />
            </EventBox>

            <EventBox
              title="应急资源"
              style="flex: 1; min-height: 0px; margin-top: 16px"
              key="resource-box"
              :data="monitorData.dispatchTaskTeamInfoDTOList || []"
              :options="scrollOptions"
              :scrollable="monitorData?.dispatchTaskTeamInfoDTOList?.length > 1"
            >
              <DispatchTeamCard
                v-for="(item, index) in monitorData.dispatchTaskTeamInfoDTOList"
                :key="index"
                :item="item"
                :dict="dictObj?.['team_busi_type']"
              />
            </EventBox>
          </transition-group>
        </BaseDelay>
      </div>
    </div>

    <div class="cover"></div>

    <!-- 视频展示 -->
    <DialogBox v-if="showVideo" :visible.sync="showVideo" title="视频">
      <video
        controls
        :src="currentVideoSrc"
        style="height: 100%; width: 100%"
      ></video>
    </DialogBox>
    <!-- 连线 打电话 -->
    <DialogBox
      @close="closeWebrtc"
      v-if="showWebrtcVideo"
      :visible.sync="showWebrtcVideo"
      title="视频连线"
      width="1158px"
      height="780px"
    >
      <div class="webrtc-video-box">
        <div data-ccbar-webrtc-video="remote" class="webrtc-video-remote"></div>
        <div data-ccbar-webrtc-video="local" class="webrtc-video-local"></div>
        <div class="webrtc-video-ctrl">
          <div @click="switchSound" title="声音" class="webrtc-video-btn">
            <div class="ctrl-icon ctrl-sound"></div>
            <span>声音</span>
          </div>
          <div @click="clearcall" title="挂机" class="webrtc-video-btn">
            <div class="ctrl-icon ctrl-clearcall"></div>
            <span>挂机</span>
          </div>
          <div @click="screenshot" title="截图" class="webrtc-video-btn">
            <div class="ctrl-icon ctrl-screenshot"></div>
            <span>截图</span>
          </div>
        </div>

        <div v-if="base64Img" class="webrtc-sceenshot">
          <div>
            <img :src="base64Img" alt="" />
          </div>
          <el-button @click="downloadImg">下载</el-button>
        </div>
      </div>
    </DialogBox>
    <!-- 全息情报 -->
    <DialogBox
      v-if="showInfomation"
      :visible.sync="showInfomation"
      :title="editableTabsValue"
      @close="showInfomation = false"
    >
      <template #right-header>
        <div class="box-tab">
          <div
            class="tab-btn"
            :class="{ active: editableTabsValue == '全息信息' }"
            @click="editableTabsValue = '全息信息'"
          >
            <span>全息情报</span>
          </div>
          <div
            class="tab-btn"
            :class="{ active: editableTabsValue == '处置时间轴' }"
            @click="editableTabsValue = '处置时间轴'"
          >
            <span>处置时间轴</span>
          </div>
        </div>
      </template>

      <el-tabs v-model="editableTabsValue" type="card" class="monitor-tab">
        <el-tab-pane key="全息信息" label="全息信息" name="全息信息">
          <div class="taskInfoList">
            <div
              v-for="(item, index) in monitorData.taskRecordDetail"
              :key="index"
              class="card-box"
            >
              <div>
                <div class="card-box-title blaze-text">
                  <img
                    style="vertical-align: middle"
                    src="./images/icon-inf.png"
                    alt=""
                  />{{ item.taskTitle }}
                </div>
                <div class="card-box-tags"></div>
              </div>

              <div class="card-box-detail">
                <table class="event-table">
                  <tr>
                    <th>时间</th>
                    <td class="w-140">{{ item.logTime }}</td>
                    <th>发送人</th>
                    <td>{{ item.logUser }}</td>
                  </tr>
                  <tr>
                    <th>上报文字</th>
                    <td colspan="3">{{ item.logMemo }}</td>
                  </tr>
                  <tr>
                    <th>图片视频</th>
                    <td colspan="3">
                      <div
                        v-if="item.logFile"
                        @click="showImgSrc(item.logFile)"
                        class="event-video"
                      >
                        <img :src="pvImg(item.logFile)" alt="" />
                      </div>
                      <div
                        v-if="item.logVideoFile"
                        @click="showVideoSrc(item.logVideoFile)"
                        class="event-video"
                      >
                        <i
                          style="font-size: 32px"
                          class="el-icon-video-play"
                        ></i>
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane key="处置时间轴" label="处置时间轴" name="处置时间轴">
          <el-timeline class="monitor-timeline2">
            <el-timeline-item
              v-for="(item, index) in monitorData.taskRecord"
              :key="index"
              placement="top"
            >
              <div
                class="el-timeline-item__timestamp"
                v-html="fTime(item.createTime)"
              ></div>
              <div class="timeline2-box">
                <span class="blaze-text"
                  >[{{ taskStatusDict(item.taskStatus) }}]{{
                    item.taskTitle
                  }}</span
                >
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
      </el-tabs>
    </DialogBox>
    <!-- 指挥参考 -->
    <DialogBox
      v-if="showCommande"
      :visible.sync="showInfomation"
      :title="editableTabsValue"
      @close="showCommande = false"
    >
      <template #right-header>
        <div class="box-tab">
          <div
            class="tab-btn"
            :class="{ active: activeTab == 't1' }"
            @click="activeTab = 't1'"
          >
            <span>建议措施</span>
          </div>
          <div
            class="tab-btn"
            :class="{ active: activeTab == 't2' }"
            @click="activeTab = 't2'"
          >
            <span>相关法规</span>
          </div>
          <div
            class="tab-btn"
            :class="{ active: activeTab == 't3' }"
            @click="activeTab = 't3'"
          >
            <span>资源要求</span>
          </div>
        </div>
      </template>

      <div v-show="activeTab == 't1'" class="box-content">
        <el-timeline class="monitor-timeline">
          <el-timeline-item
            v-for="(item, index) in exePlanMeasuresList"
            :key="index"
            placement="top"
          >
            <div class="card-dot" slot="dot">
              <img src="./images/timeline-num.png" alt="" />
              <span class="blaze-text">{{ index + 1 }}</span>
            </div>

            <div class="card-timeline-box">
              <div class="card-timeline-box-title blaze-text">
                {{ item.title }}
              </div>
              <div class="card-timeline-box-content">{{ item.memo }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div v-show="activeTab == 't2'" class="box-content">
        <template v-if="eventReference.exePlanLawsList.length > 0">
          <div
            v-for="(item, index) in eventReference.exePlanLawsList"
            :key="index"
            class="list-rule"
          >
            <div class="list-rule-title">
              <span class="list-rule-num">
                <font class="blaze-text">{{ index + 1 }}</font>
              </span>
              <span class="blaze-text" style="font-size: 18px">{{
                item.fileName
              }}</span>
            </div>
            <div class="list-rule-info">
              <table class="event-table">
                <tr>
                  <th>上传时间</th>
                  <td>{{ item.createTime }}</td>
                </tr>
              </table>
            </div>
          </div>
        </template>
        <div v-else>
          <p style="text-align: center">暂无相关法规</p>
        </div>
      </div>
      <div v-show="activeTab == 't3'" class="box-content">
        <div class="list-res">
          <div class="list-res-title">
            <span class="blaze-text">车辆要求</span>
          </div>
          <div class="list-res-content">
            <div
              v-html="
                convertNewlineToBr(
                  eventReference.jrEmergencyExePlanResource.teamReq
                )
              "
            ></div>
          </div>
        </div>

        <div class="list-res">
          <div class="list-res-title">
            <span class="blaze-text">人员要求</span>
          </div>
          <div class="list-res-content">
            <div
              v-html="
                convertNewlineToBr(
                  eventReference.jrEmergencyExePlanResource.personReq
                )
              "
            ></div>
          </div>
        </div>

        <div class="list-res">
          <div class="list-res-title">
            <span class="blaze-text">物资要求</span>
          </div>
          <div class="list-res-content">
            <div
              v-html="
                convertNewlineToBr(
                  eventReference.jrEmergencyExePlanResource.resourceReq
                )
              "
            ></div>
          </div>
        </div>
      </div>
    </DialogBox>
    <!-- 视频会议 -->
    <DialogBox
      v-if="showVideoConference"
      :visible.sync="showVideoConference"
      title="发起视频会议"
      @close="handleCloseVideoConferenceDialog"
    >
      <videoConference
        ref="videoConference"
        @handleAction="handleAction"
        :data1="conference.data1"
        :data2="conference.data2"
        :initialtheme="disasterEventData.disasterEventName"
      />
      <template #footer>
        <el-button @click="handleCloseVideoConferenceDialog">取消</el-button>
        <el-button type="primary" @click="initConference">发起会议</el-button>
      </template>
    </DialogBox>

    <DialogBox
      v-if="showQrCode"
      :visible.sync="showQrCode"
      title="会议信息"
      style="width: auto; height: 500px"
      @close="showQrCode = false"
    >
      <div v-if="videoConferenceInfo" class="qr-code-dialog-content">
        <p>会议名称：{{ videoConferenceInfo.reservation_id.subject }}</p>
        <p class="qr-code-link-paragraph">
          会议链接：<a
            :href="videoConferenceInfo.public_link"
            target="_blank"
            >{{ videoConferenceInfo.public_link }}</a
          >
        </p>
        <img
          :src="`data:image/png;base64,${videoConferenceInfo.qr_base}`"
          alt="会议二维码"
          class="qr-code-image"
        />
      </div>
    </DialogBox>

    <!-- 视频会议选择抽屉 -->
    <DrawerBox
      v-if="showDialog"
      style="width: 600px; height: 100%"
      :title="action === 1 ? '选择主持' : '增加人员'"
      :visible="showDialog"
      :headerType="2"
      @close="showDialog = false"
    >
      <Transfer
        :multiple="action !== 1"
        :type="action"
        @close="showDialog = false"
        @submit="handleSubmit"
      />
    </DrawerBox>

    <!-- 全屏监控 -->
    <FullVideo
      v-if="showFullVideo"
      @close="closeWebrtc"
      :item="lastVideoItem"
    />
  </Layout>
</template>

<script>
import { getAndSetDict } from "@/utils/dict";
import { get, post } from "@/http/request";
import { createVideoConference } from "@/api/auth";
import eventBus from "@/utils/eventbus.js";

import Layout from "@/layout";
import Map from "./components/map.vue";
// import Table from "./components/table.vue";
import EventBox from "./components/eventBox.vue";
import DialogBox from "./components/DialogBox.vue";
import DrawerBox from "./components/DrawerBox.vue";
import MissionCardItem from "./components/MissionCardItem.vue";
import videoConference from "./components/videoConference.vue";
import Transfer from "./business/transfer.vue";
import CamList from "./components/CamList.vue";
import DispatchTeamCard from "./components/DispatchTeamCard.vue";
import FullVideo from "./components/FullVideo.vue";

//import LiveItem from "./components/LiveItem.vue";
import SSE from "@/http/sse.js";
import WebrtcClient from "@/utils/webrtc.js";

import testd from "./testdata.json";
import testd2 from "./testdata2.json";
import testd3 from "./testdata3.json";

function downloadBase64File(base64String, fileName) {
  fileName =
    fileName || "screenshot-" + new Date().format("yyyy-MM-dd") + ".png";
  var link = document.createElement("a");
  link.setAttribute("href", base64String);
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export default {
  components: {
    Layout,
    Map,
    // Table,
    EventBox,
    DialogBox,
    DrawerBox,
    Transfer,
    MissionCardItem,
    videoConference,
    DispatchTeamCard,
    CamList,
    FullVideo,
    // LiveItem
  },
  data() {
    return {
      activeTab: "t1",
      clientId: "",
      dictObj: {},
      monitorData: {},
      isMapDataInit: false,
      editableTabsValue: "全息信息",
      disasterEventDataInit: false,
      disasterEventData: {}, //事件简报
      eventReference: {}, //指挥参考
      currentVideoSrc: "",
      currentImgSrc: "",
      showWebrtcVideo: false,
      showVideo: false,
      showInfomation: false, // 展示 全息情报 弹窗
      showCommande: false, // 展示 指挥参考 弹窗
      showVideoConference: false, // 展示 视频会议 弹窗
      base64Img: "",
      action: "",
      showDialog: false,
      conference: {
        subject: "",
        conferenceTime: [],
        data1: [],
        data2: [],
      },
      deletedItems: new Set(), // 存储被删除项的ID
      scrollOptions: {
        limitMoveNum: 2,
        step: 0.15,
        waitTime: 1000,
        hoverStop: true,
      },
      lastVideoClick: "", // 最后点击的视频类型
      showFullVideo: false, // 是否全屏
      lastVideoItem: {}, // 最后点击的视频元素

      showQrCode: false,
      videoConferenceInfo: {
        time_remaining: 7199,
        reservation_id: {
          config_start_time: "2025-05-22 13:47",
          config_timezone: "Asia/Shanghai",
          creator_first_name: "",
          subject: "政府有人晕倒",
          open_calendar: "no",
          description: "",
          recording: "no",
          utc_start_time: "2025-05-22 05:47",
          cycle: "none",
          is_random_conf: "yes",
          call_member: "yes",
          remoteid: "0",
          conf_reservation_status: "inuse",
          ucm_timezone: "( UTC+08:00 ) Asia/Shanghai",
          invite_by_noadmin: "yes",
          members: [
            {
              member_type: 1,
              last_name: "",
              member_name: "",
              log_index: 59,
              reservation_id: "56d90454-b5aa-4673-99aa-cc2a876222cf",
              is_admin: "yes",
              access_token: "",
              real_member_name: "",
              member_extension: "admin",
              name: "admin",
              location: "local",
              comment: "",
              state: "needsAction",
              first_name: "",
              email: "",
              real_email: "",
            },
          ],
          attending: [],
          host: "admin",
          utc_end_time: "0000-00-00 00:00",
          displaying: "no",
          host_first_name: "",
          kick_time: "30",
          pincode: "",
          creator: "admin",
          email_remind_time: "60",
          videoing: "no",
          end_time: "0000-00-00 00:00",
          conf_timeout: 12,
          allow_user_unmute: "no",
          host_last_name: "",
          reservation_id: "56d90454-b5aa-4673-99aa-cc2a876222cf",
          conf_execute_status: "30",
          start_time: "2025-05-22 13:47",
          conf_type: "video",
          conf_number: "30001652",
          admin_pincode: "",
          config_timezone_val: "+8.0",
          join_early: "10",
          meeting_duration: 0,
          config_end_time: "0000-00-00 00:00",
          enable_remind: "yes",
          creator_last_name: "",
          enable_mcu: "no",
          cycle_interval: 0,
        },
        start_time: "",
        ucm_timezone: "Asia/Shanghai",
        expiration_time: "2025-05-22 15:47",
        lan1_link: "",
        expiration_time_utc: "2025-05-22 07:47",
        public_link:
          "https://ec74d715bf00.gdms.hlink.online/#/invite/anonymous/30001652/1747892847/0/",
        lan_link:
          "https://***********:8090/#/invite/anonymous/30001652/1747892847/0/",
        qr_base:
          "iVBORw0KGgoAAAANSUhEUgAAAHsAAAB7AQMAAABuCW08AAAABlBMVEUAAAD///+l2Z/dAAAAAnRSTlP//8i138cAAAAJcEhZcwAACxIAAAsSAdLdfvwAAAFzSURBVEiJ1dW7jcMwDAZgBircxQsI0BrqtJK9QGwvEK2kTmsY8AJSp0Iw73ecB3CNeMXhcEIARx9gmqEYmvjbov8MiWhi3RfjA11kkLmOUSW3jhHfZRD0xCYHlSONYhg6TrZefgI3a3YyXg5cB2s44rHv1BuAeoxRH59PgRqAtRN7XvvPMTQgkSbLd1vJbl4GO67BcDEz1YsMcsQRmYX1zZ0xJMBcmIOabZ2EEPSVGLubUywDjttSwOwDLzLYUQmHn2WYjZdBghHCrFM8MxWAOxpn7nh2zxhNyLztTiGSD9sig93RtTOz08PrbJuQnNrRd0XxO9MW5LAOzqAqY3lm2oTkTKINVb93Z6ZtyKX26KCop/LsIAGgJGg66nmdhBBXsmjSOpDJMsBKnX7szmHQBvyzB4uDPUZOLwPMD4wcH9QjWRlgsFmE+aQugTFuidDa+iIG3H1HH9HGQsBcPwqPnZ5k8KiHOjqIX2OrBb/ynvsT+AJxKXhBUNDzzgAAAABJRU5ErkJggg==",
        qr_full:
          "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",
        lan2_link: "",
      },
    };
  },
  created() {
    this.clientId = this.$route.params.id;
    if (process.env.NODE_ENV === "development") {
      // console.log('route',this.$route.params.id    )
      // this.monitorData = testd.data;
      // this.disasterEventData = testd2.data;
      // this.disasterEventDataInit = true;
      // this.eventReference = testd3.data;
      // this.isMapDataInit = true;
      // console.log("数据", this.monitorData);
      console.log("开发环境");
      this.getData();
      this.initSSE();
    } else if (process.env.NODE_ENV === "production") {
      console.log("生产环境");
      this.getData();
      this.initSSE();
    }

    this.initEvent();

    window.testApp = this;
    window.WebrtcClient = WebrtcClient;
  },
  beforeDestroy() {
    // 组件销毁时关闭连接
    if (this.sseClient) {
      this.sseClient.close();
      this.sseClient = null;
    }

    WebrtcClient.destroy();
  },
  computed: {
    exePlanMeasuresList() {
      let temp = this.eventReference.exePlanMeasuresList || [];
      return temp.sort(function (a, b) {
        return a.idxOrder - b.idxOrder;
      });
    },
  },
  methods: {
    initEvent() {
      let _self = this;
      WebrtcClient.init();

      eventBus.$on("webrtcConnect", function (data) {
        console.log("==>接通", data, _self.lastVideoClick);

        if (_self.lastVideoClick == "full") {
          _self.showFullVideo = true;
        } else if (_self.lastVideoClick == "infowindow") {
          //_self.showWebrtcVideo = true;
        } else {
          _self.showWebrtcVideo = true;
        }

        _self.$nextTick(function () {
          updateVide(data);
        });
      });

      eventBus.$on("webrtcHangup", function (data) {
        console.log("挂断");
        _self.showWebrtcVideo = false;
        _self.showFullVideo = false;
      });

      eventBus.$on("webrtcVideoClickTypeChange", function (data) {
        console.log("视频类型", data);
        _self.lastVideoClick = data;
      });

      function updateVide(list) {
        list.forEach(function (item) {
          var tVideo = document.createElement("video");
          tVideo.srcObject = item.stream;
          tVideo.autoplay = true;
          tVideo.playsinline = "playsinline";
          tVideo.muted = item.type == "local"; //||item.type=='remote'&&ctx.speakerMuted;
          tVideo.oncanplay = function () {
            this.play();
          };

          if (item.type == "remote") {
            _self.remoteStream = item.stream;
            window.$('[data-ccbar-webrtc-video="remote"]').html(tVideo);
            console.log("更新远端视频");
          } else {
            console.log("更新本地视频");

            window.$('[data-ccbar-webrtc-video="local"]').html(tVideo);
          }
        });
      }
    },
    clearcall() {
      WebrtcClient.clearCall();
    },
    closeWebrtc() {
      this.showWebrtcVideo = false;
      this.showFullVideo = false;
      window.$(".layout_header").show();
      this.lastVideoClick = "";
      try {
        //this.clearcall();
      } catch (e) {}
    },
    switchSound() {
      window.yqWebrtcApp && window.yqWebrtcApp.Device.switchMicro();
    },
    screenshot() {
      let _self = this;
      window.yqWebrtcApp &&
        window.yqWebrtcApp.Device.takePhoto(_self.remoteStream, true)
          .then((base64) => {
            _self.base64Img = base64;
          })
          .catch((er) => {});
    },
    makecall(num) {
      num =
        typeof num == "string" || typeof num == "number"
          ? num
          : "34020000001320000002";
      console.log("num.", num);
      try {
        WebrtcClient.clearCall();
      } catch (e) {
        console.error(e);
      }

      WebrtcClient.makecall(num);
    },
    makecallFull(item) {
      console.log("item", item);
      this.lastVideoItem = item;
      eventBus.$emit("webrtcVideoClickTypeChange", "full");
      this.makecall(item.code);
    },
    downloadImg() {
      downloadBase64File(this.base64Img);
      this.base64Img = "";
    },
    convertNewlineToBr(text) {
      if (!text) return "";

      return (
        text
          // 去掉开头和末尾的换行符
          .trim()
          // 将连续的多个换行符替换为单个换行符
          .replace(/\n{2,}/g, "\n")
          // 将剩余的换行符替换为br标签
          .replace(/\n/g, "<br>")
      );
    },
    taskStatusDict(key) {
      let keyMap = {
        1: "待处理",
        2: "进行中",
        3: "已处理",
        4: "开始执行任务",
        5: "到达现场",
      };
      return keyMap[key] || key;
    },
    dict(value, key) {
      return this.dictObj[key] && this.dictObj[key][value]
        ? this.dictObj[key][value]
        : value;
    },
    imgPv(item, index, start) {
      const groupedData = {
        0: "sxt", // 摄像头
        1: "zhidui", // 分队
        2: "wrj", // 无人机
        3: "zhongduan", // 手持设备终端
      };
      return `images/pv/${index + start}.png`;
    },
    imgIcon(item) {
      const groupedData = {
        0: "sxt", // 摄像头
        1: "zhidui", // 分队
        2: "wrj", // 无人机
        3: "zhongduan", // 手持设备终端
      };
      return `images/svg-${groupedData[item.eqType] || "sxt"}.svg`;
    },
    fTime(time) {
      return time.replace(" ", "<br>");
    },

    async getData() {
      getAndSetDict(
        "event_level,RESCUE_EVENT,team_busi_type",
        this.dictObj
      ).then((err, res) => {
        console.log("dict", err, res, this.dictObj);
      });
      const [err, res] = await get(
        "/jr-rescuenet/api/event/monitor/getDisasterEventById/" + this.clientId
      );
      console.log("getDisasterEventById", res);
      if (res) {
        this.disasterEventDataInit = true;

        this.disasterEventData = res;
      }

      const [err2, res2] = await get(
        "/jr-rescuenet/api/event/monitor/getEventReference/" + this.clientId
      );
      console.log("getEventReference", res2);
      if (res2) {
        this.eventReference = res2;
      }
    },
    msgHandel(data) {
      console.log("data==>", data);
      // 过滤掉已被删除的项
      const filteredData = {
        ...data.data,
        aroundMgrList: data.data.aroundMgrList.filter(
          (item) => !this.deletedItems.has(item.id)
        ),
        dynamicMgrList: data.data.dynamicMgrList.filter(
          (item) => !this.deletedItems.has(item.id)
        ),
      };
      this.monitorData = filteredData;
      this.isMapDataInit = true;
    },
    backToList() {
      this.$router.push({
        path: "/dispatch-monitor",
      });
    },
    showImgSrc(id) {
      let img = "/jr-rescuenet/api/file/preview/" + id;
      window.layer.photos({
        photos: {
          title: "现场图片",
          start: 0,
          data: [
            {
              alt: "图片",
              pid: 5,
              src: img,
            },
          ],
        },
        footer: false, // 是否显示底部栏 --- 2.8.16+
      });
    },
    showVideoSrc(id) {
      let src = "/jr-rescuenet/api/file/preview/" + id;
      this.currentVideoSrc = src;
      this.showVideo = true;
    },
    pvImg(id) {
      //图片
      return "/jr-rescuenet/api/file/preview/" + id;
    },
    callPhone(num) {},
    initSSE() {
      if (this.sseClient) {
        return;
      }
      this.sseClient = new SSE("eventMonitor:" + this.clientId, {
        withCredentials: true,
        enableSSE: false,
        url:
          "/jr-rescuenet/api/event/monitor/getEventMonitorDTO/" + this.clientId,
        pollConfig: {},
        msgHandler: this.msgHandel,
      });
      this.sseClient.connect();
    },
    handleCloseVideoConferenceDialog() {
      this.showVideoConference = false;
      if (this.$refs.videoConference) {
        this.$refs.videoConference.resetForm();
      }
    },
    handleAction(type) {
      this.showDialog = true;
      this.action = type;
    },
    handleSubmit(list) {
      this.showDialog = false;
      if (this.action === 1) {
        this.conference.data1 = list;
      } else {
        this.conference.data2 = list;
      }
    },
    async initConference() {
      // 获取 videoConference 组件的实例
      const videoConferenceComponent = this.$refs.videoConference;
      // 调用 getConferenceInfo 方法，获取 theme 和 conferenceTime
      const {
        type = 1,
        subject = "",
        password = "",
        start_time = null,
        end_time = null,
      } = (await videoConferenceComponent?.getConferenceInfo()) || {};

      const params = {
        type,
        subject: subject,
        cookie: this.$store.getters.videoCookie,
      };

      if (params.type === 0) {
        params.pwd = password;
        params.guests = this.conference.data2.map((item) => {
          const guest = {
            area: "86",
            phone_number: item.contacts.phone,
            guest_name: item.name,
          };
          return guest;
        });
        params.start_time = start_time;
        params.end_time = end_time;
      }

      const [err, res] = await createVideoConference(params);

      if (err) {
        this.$message.error(err.message || "会议创建失败");
        return;
      }

      this.videoConferenceInfo = res.data.response;

      const { public_link, qr_base } = res.data.response;

      // 新窗口打开会议链接
      if (public_link) {
        window.open(public_link, "_blank");
      }

      if (qr_base) {
        this.showVideoConference = false;
        this.$nextTick(() => {
          this.showQrCode = true;
        });
      } else {
        // 如果没有二维码数据，可以选择清空或者提示
        this.$message.info("但未返回二维码信息。");
      }
    },
    removeCamera(item) {
      this.deletedItems.add(item.id);
      this.monitorData.aroundMgrList = this.monitorData.aroundMgrList.filter(
        (i) => i.id != item.id
      );
      this.monitorData.dynamicMgrList = this.monitorData.dynamicMgrList.filter(
        (i) => i.id != item.id
      );
    },
    handleToWall(data) {
      // 从删除集合中移除该项
      this.deletedItems.delete(data.id);

      if (data.eqType == 2) {
        // 判断是否存在
        const index = this.monitorData.dynamicMgrList.findIndex(
          (i) => i.id == data.id
        );
        if (index == -1) {
          this.monitorData.dynamicMgrList.push(data);
        }
      } else if (data.eqType == 0) {
        // 判断是否存在
        const index = this.monitorData.aroundMgrList.findIndex(
          (i) => i.id == data.id
        );
        if (index == -1) {
          this.monitorData.aroundMgrList.push(data);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";

.monitor-container {
  @include full;
  font-size: 14px;
  overflow: hidden;

  .box-content {
    overflow: auto;
  }

  .left-panel {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 2;
    width: 558px;
    padding: 16px 88px 24px 24px;
    box-sizing: border-box;
    background: url("@/assets/images/left-panel-bg.png") no-repeat -73px 0px / calc(
        100% + 115px
      ) calc(100% + 10px);
  }

  .right-panel {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    height: 100%;
    width: 550px;
    padding: 16px 24px 24px 88px;
    // background-color: #001650 !important;
    background: url("@/assets/images/right-panel-bg.png") no-repeat -40px 0px / calc(
        100% + 115px
      ) calc(100% + 10px);
    gap: 16px;
  }
}

.cam-box {
  display: inline-block;
  vertical-align: top;
  width: 208px;
  color: #fff;
  border-radius: 4px;
  margin-bottom: 10px;
  opacity: 1;
  background: linear-gradient(
    180deg,
    rgba(99, 184, 190, 0.2) 0%,
    rgba(99, 184, 190, 0.05) 100%
  );

  flex-grow: 0;

  &:nth-child(odd) {
    margin-right: 10px;
  }

  .cam-box-img {
    height: 99px;
    margin: 8px;
    background-color: #fff;
    border-radius: 2px;
  }

  .cam-box-detail {
    line-height: 20px;
  }

  .cam-box-type {
    padding: 0 5px;
  }

  .cam-box-name {
    padding: 0 5px;
    flex: 1;
  }

  .cam-box-icon {
    padding: 0 5px;
    color: #5d8c8f;
    font-size: 20px;
  }
}

.box-center-main {
  width: 100%;
  height: 100%;
  position: relative;
}

.card-timeline-box {
  // width: 360px;
  // height: 217px;
  padding: 16px;
  opacity: 1;
  box-sizing: border-box;
  border: 1px dashed;
  color: #fff;
  background: linear-gradient(
    270deg,
    rgba(4, 203, 216, 0.05) 0%,
    rgba(4, 203, 216, 0.1) 100%
  );
  border-image: linear-gradient(
      0deg,
      rgba(4, 203, 216, 0.3) 0%,
      #04cbd8 72%,
      #ffffff 100%
    )
    1;

  .card-timeline-box-title {
    font-size: 18px;
    margin-bottom: 10px;
  }

  // .card-timeline-box-content{}
}

.card-box {
  position: relative;
  min-height: 170px;
  padding: 16px 30px 16px 16px;
  margin-bottom: 16px;
  background: linear-gradient(
    270deg,
    rgba(4, 203, 216, 0.05) 0%,
    rgba(4, 203, 216, 0.1) 100%
  );

  &:last-child {
    margin-bottom: 0;
  }

  .card-box-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .card-box-img {
    margin-right: 14px;
    flex-shrink: 0;
  }

  .card-box-info {
    flex: 1;
  }

  .card-box-title {
    font-size: 18px;

    img {
      margin-right: 8px;
    }
  }

  .card-box-tags {
    .tag {
      position: relative;
      background: rgba(4, 203, 216, 0.1);
      line-height: 24px;
      padding: 0 7px;
      margin-right: 8px;

      &:after {
        right: 0;
        content: "";
        position: absolute;
        width: 2px;
        height: 12px;
        background: linear-gradient(
          rgba(255, 255, 255, 1),
          rgba(4, 203, 216, 1)
        );
        top: 50%;
        margin-top: -6px;
      }

      &:before {
        left: 0;
        content: "";
        position: absolute;
        width: 2px;
        height: 12px;
        background: linear-gradient(
          rgba(255, 255, 255, 1),
          rgba(4, 203, 216, 1)
        );
        top: 50%;
        margin-top: -6px;
      }
    }
  }
}

// .card-box-detail{}

.card-dot .blaze-text {
  position: absolute;
  font-size: 20px;
  left: 25px;
  top: 8px;
}

.blaze-text2 {
  color: #ffdc00;
}

.blaze-text3 {
  background: #00ffff;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.event-table {
  width: 330px;
  font-size: 14px;
  color: #fff;
  vertical-align: top;

  td,
  th {
    padding: 5px 7px;
    vertical-align: top;
    line-height: 24px;
  }

  th {
    color: #5d8c8f;
    width: 70px;
  }
}

.event-table-tag {
  display: inline-flex;
  align-items: center;
  position: relative;
  padding-right: 30px;

  font {
    margin-left: 8px;
  }

  &:after {
    content: "";
    display: inline-block;
    vertical-align: middle;
    position: absolute;
    right: 16px;
    top: 2px;
    opacity: 0.2;
    height: 16px;
    width: 1px;
    background: #04cbd8;
  }

  &:last-child {
    padding-right: 0;

    &:after {
      display: none;
    }
  }
}

.list-rule {
  border-radius: 4px;
  min-height: 90px;
  padding: 16px;
  margin-bottom: 10px;
  background: linear-gradient(
    90deg,
    rgba(4, 203, 216, 0.1) 0%,
    rgba(4, 203, 216, 0.05) 100%
  );

  .list-rule-num {
    display: inline-block;
    margin-right: 10px;
    background: linear-gradient(
      270deg,
      rgba(4, 203, 216, 0) 0%,
      rgba(4, 203, 216, 0.5) 50%,
      rgba(4, 203, 216, 0) 100%
    );
    width: 40px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    font-size: 18px;
  }
}

.list-res {
  // padding: 0 16px 16px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  background: rgba(4, 203, 216, 0.05);
  position: relative;

  .list-res-title {
    height: 40px;
    background: url(./images/title2.png) no-repeat center;
    width: 140px;
    line-height: 40px;
    padding: 0 18px;
    position: absolute;
  }

  .list-res-content {
    color: #fff;
    padding: 50px 16px 16px 16px;

    br {
      margin-bottom: 16px;
    }
  }
}

.event-report {
  .event-report-img {
    float: left;
    width: 80px;
    min-height: 80px;
    margin-right: 16px;
  }

  .event-report-title {
    min-height: 80px;
    font-size: 24px;
    font-weight: bold;
  }

  .event-report-time {
    color: #fff;

    .color2 {
      color: #5d8c8f;
    }

    font-size: 14px;
  }

  .blaze-text {
    font-size: 24px;
    line-height: 36px;
    margin-bottom: 10px;
  }

  .event-report-detail {
    padding: 16px;
  }
}

.buttons {
  height: 40px;
  margin: 24px 0;

  .monitor-button {
    width: 136px;
    line-height: 40px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
    background: url("@/assets/images/monitor-button.png") center / contain
      no-repeat;
  }
}

.event-video {
  margin-right: 8px;
  margin-bottom: 8px;
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border: 1px solid #fff;
  text-align: center;
  vertical-align: middle;
  line-height: 40px;

  img {
    width: 100%;
    height: 100%;
  }
}

.box-tab {
  font-size: 16px;
  line-height: 55px;
  display: flex;
  gap: 16px;

  &:after {
    content: "";
    width: 1px;
    height: 26px;
    opacity: 0.2;
    background: #ffffff;
    display: inline-block;
    vertical-align: middle;
  }

  .tab-btn {
    position: relative;
    width: 112px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;

    span {
      font-family: "Alibaba PuHuiTi 3.0", sans-serif;
      font-size: 16px;
      line-height: 24px;
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s;
      text-align: center;
    }

    &:not(.active) {
      /* 非活跃状态下的样式 */
      background: linear-gradient(
        180deg,
        rgba(0, 128, 255, 0) 0%,
        rgba(0, 128, 255, 0.2) 100%
      );
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-image: linear-gradient(
          180deg,
          rgba(0, 128, 255, 0.2) 0%,
          rgba(0, 128, 255, 0.5) 100%
        )
        1;
    }

    &.active {
      background: transparent;
      border: 1px solid #00aaff;
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.7) 0%,
        rgba(0, 170, 255, 0.2) 100%
      );

      &::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 80px;
        height: 2px;
        transform: translateX(-50%);
        background: linear-gradient(
          90deg,
          rgba(112, 222, 255, 0) 0%,
          rgba(112, 222, 255, 0.8) 50%,
          rgba(112, 222, 255, 0) 100%
        );
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        width: 80px;
        height: 2px;
        transform: translateX(-50%);
        background: linear-gradient(
          90deg,
          rgba(112, 222, 255, 0) 0%,
          rgba(112, 222, 255, 0.8) 50%,
          rgba(112, 222, 255, 0) 100%
        );
      }

      span {
        font-weight: bold;
        background: linear-gradient(180deg, #ffffff 39%, #70deff 79%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }
}

.box-tab-container {
  background: rgba(4, 203, 216, 0.05);
  padding: 16px;
}

.box-center-back {
  position: absolute;
  z-index: 2;
  top: 40px;
  left: 536px;
  width: 156px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 24px;
  cursor: pointer;

  background: linear-gradient(
    180deg,
    rgba(0, 170, 255, 0.05) 0%,
    rgba(0, 170, 255, 0.3) 100%
  );
  border-radius: 4px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(0, 170, 255, 0.2) 0%, #00aaff 100%)
    1;
  box-shadow: inset 0px 0px 10px 0px rgba(0, 170, 255, 0.4);

  i {
    margin-right: 8px;
    font-size: 16px;
    color: #70deff;
  }

  span {
    font-family: "Alibaba PuHuiTi 3.0", sans-serif;
    font-size: 16px;
    line-height: 24px;
    background: linear-gradient(180deg, #ffffff 0%, #70deff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
}

.event-distance {
  position: absolute;
  right: 15px;
  top: 120px;
}

.layout-footer {
  pointer-events: none;
}

.webrtc-video-box {
  .webrtc-sceenshot {
    text-align: center;
    position: absolute;
    top: 20px;
    left: 20px;

    img {
      max-width: 320px;
      max-height: 320px;
    }
  }

  .webrtc-video-remote {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .webrtc-video-local {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 160px;
    height: 240px;
    z-index: 3;
    border: 1px solid #ddd;
  }

  .webrtc-video-ctrl {
    position: absolute;
    border-radius: 0px 0px 4px 4px;
    opacity: 1;
    background: rgba(3, 25, 39, 0.5);
    height: 116px;
    padding-top: 30px;
    width: 100%;
    left: 0;
    bottom: -20px;
    z-index: 2;
    text-align: center;

    .webrtc-video-btn {
      margin: 0 36px;
      display: inline-block;
      width: 56px;
      cursor: pointer;
      height: 86px;

      span {
        display: block;
        line-height: 30px;
        color: #fff;
      }

      .ctrl-icon {
        color: #fff;
        margin: 0 auto;
        width: 55px;
        height: 55px;

        &.ctrl-sound {
          background: url(./images/icon-sound.png) no-repeat center;
          background-size: 40px 40px;
        }

        &.ctrl-clearcall {
          background: url(./images/icon-clearcall.png) no-repeat center;
          background-size: 55px 55px;
        }

        &.ctrl-screenshot {
          background: url(./images/icon-screenshot.png) no-repeat center;
          background-size: 40px 40px;
        }
      }
    }
  }
}

.taskInfoList {
  overflow-y: auto;
  height: 100%;
  max-height: calc(100vh - 350px);
}

.w-140 {
  width: 150px;
}
</style>

<style lang="scss">
.monitor-timeline.el-timeline .el-timeline-item__tail {
  border-left: 2px dashed rgba(4, 203, 216, 0.5);
  left: 32px;
}

.monitor-timeline.el-timeline .el-timeline-item__wrapper {
  padding-left: 74px;
}

.monitor-timeline2 {
  .el-timeline-item__node {
    background: url(./images/timeline.png) no-repeat;
    width: 125px;
    height: 40px;
    top: 11px;
    left: 70px;
  }

  .el-timeline-item__tail {
    position: absolute;
    left: 80px;
    top: 32px;
    background: none;
    border-left: 2px dashed #097580;
  }

  .el-timeline-item__timestamp {
    position: absolute;
    left: 0;
    color: #fff;
    top: 10px;
    line-height: 1.5;
  }

  .el-timeline-item__wrapper {
    padding-left: 200px;
  }
}

.timeline2-box {
  line-height: 56px;
  padding: 0 20px;
  border-radius: 4px;
  opacity: 1;
  background: linear-gradient(
    270deg,
    rgba(4, 203, 216, 0) 0%,
    rgba(4, 203, 216, 0.1) 100%
  );
}

.monitor-tab .el-tabs__header {
  display: none;
}

.webrtc-video-box {
  video {
    width: 100%;
    height: 100%;
    background: #000;
  }
}

.full-video {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 3333;

  .webrtc-video-remote {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    video {
      width: 100%;
      height: 100%;
    }
  }
}

.qr-code-dialog-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 32px;
  height: 100%;
  font-size: 16px;
  gap: 24px; /* 调整间距以优化视觉效果 */
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px; /* 为内容区域添加圆角 */
}

.qr-code-dialog-content p {
  margin-bottom: 8px; /* 为段落添加一些底部间距 */
}

.qr-code-link-paragraph {
  /* 为包含链接的段落添加样式 */
  word-break: break-all; /* 确保长链接能正确换行 */
}

.qr-code-link-paragraph a {
  color: #007bff; /* 使用更鲜明的蓝色作为链接颜色 */
  text-decoration: none; /* 移除下划线，现代设计中更常见 */
  font-weight: bold; /* 加粗链接文字 */
}

.qr-code-link-paragraph a:hover {
  text-decoration: underline; /* 鼠标悬停时显示下划线 */
  color: #0056b3; /* 鼠标悬停时加深链接颜色 */
}

.qr-code-image {
  width: 280px; /* 稍微增大二维码尺寸 */
  height: 280px; /* 稍微增大二维码尺寸 */
  border: 2px solid #dee2e6; /* 使用稍深一些的边框颜色 */
  border-radius: 12px; /* 增大圆角 */
  background-color: #ffffff;
  padding: 15px; /* 调整内边距 */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  transition: transform 0.3s ease; /* 添加鼠标悬停时的过渡效果 */
}

.qr-code-image:hover {
  transform: scale(1.05); /* 鼠标悬停时轻微放大 */
}
</style>
