<template>
    <div class="event-list y-container--tight no-padding">
      <el-form
        v-if="false"
        class="search-form"
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        size="small">
        <el-form-item label="级别">
          <el-select
            v-model="searchForm.disasterLevel"
            placeholder="请选择">
            <el-option
              v-for="(label, value) in event_level"
              :key="value"
              :label="label"
              :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select
            v-model="searchForm.disasterAreaCode"
            placeholder="请选择">
            <el-option
              v-for="(label, value) in areaDict"
              :key="value"
              :label="label"
              :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字">
          <el-input
            v-model="searchForm.disasterEventName"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item class="search-form_btn">
          <el-button
            icon="el-icon-refresh"
            @click.native="handleReset"
            >重置</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.native="fetch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="data"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%;height:300px">
        <el-table-column
          prop="taskName"
          label="任务名称"
          min-width="100"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="teamName"
          label="所属" show-overflow-tooltip
          min-width="120">
          
        </el-table-column>
        <el-table-column
          prop="kilometer"
          label="当前里程"
          min-width="80">
          <template slot-scope="scope">
           <span>{{km(scope.row.kilometer)}}</span>
          </template>
        </el-table-column>
        <el-table-column 
          prop="expectedTime"
          label="预计时间"
          min-width="70">
          <template slot-scope="scope">
           <span>{{time(scope.row.expectedTime)}}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="任务负责人"
          prop="leaderName"
          min-width="70">
          
        </el-table-column>
        <el-table-column
          label="目的地经纬度"
          
          show-overflow-tooltip>
          <template slot-scope="scope">
           <span>{{scope.row.lon}},{{scope.row.lat}}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="连线操作"
          min-width="70">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click="handleDetail(scope.row)"
              ><img src="../images/link.svg" style="vertical-align: middle;">  连线</el-link
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </template>
  
  <script>
  import { getEventList } from '@/api/event'
  import { getAreaList } from '@/api/dict'
  import { getAndSetDict, event_level_status } from '@/utils'
import WebrtcClient from "@/utils/webrtc.js";
  
  export default {
    props:['data'],
    components: {
    },
    data() {
      return {
        tableData: [],
      }
    },
    computed: {},
    created() {

    },
    methods: {
      km(item){return item?(item/1000).toFixed(2)+'KM':item},
      time(item){
        return item?(item/60).toFixed(2)+'分':item
      },
      handleDetail(row) {
        // WebrtcClient.makecall(row)
        this.$emit('makecall',row.leadMobile)
        console.log(row)
      },
    },
  }
  </script>
  
  <style lang="scss" scoped>
  .event-list {
    .search-form::v-deep {
      @include flex-row;
      .el-form-item {
        @include flex-row;
        &.search-form_btn {
          flex: 0 0 auto;
        }
        .el-form-item__label {
          flex: 0 0 auto;
        }
      }
    }
  }
  </style>
  