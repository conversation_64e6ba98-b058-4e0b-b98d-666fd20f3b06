<script>
export default {
  name: 'Delay',
  props: {
    delay: {
      type: [Number, String],
      default: 0
    },
    autoStart: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isReady: false
    }
  },
  methods: {
    start() {
      return new Promise((resolve) => {
        setTimeout(() => {
          this.isReady = true
          resolve()
        }, this.delay)
      })
    }
  },
  mounted() {
    if (this.autoStart) {
      this.start()
    }
  },
  render(h) {
    if (!this.isReady) return null
    return this.$slots.default?.[0] || null
  }
}
</script>