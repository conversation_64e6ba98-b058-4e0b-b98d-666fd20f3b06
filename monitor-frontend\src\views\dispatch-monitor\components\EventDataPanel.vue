<template>
  <div class="event-data-panel">
    <BaseDelay delay="800">
      <transition
        name="fade-in-right"
        appear>
        <div class="left-side">
          <TitleBlock title="事件数据总览" />
          <DataBlock
            :data="{ label: '接报总量', count: totalCalls }"
            total
            :icon="require('@/assets/images/report-count-icon.png')" />
          <DataBlock
            :data="{ label: '事件总量', count: totalEvents }"
            total
            :icon="require('@/assets/images/event-count-icon.png')" />
          <DataBlock
            v-for="item in disasterEventStats"
            :data="item"
            :icon="item.icon" />
        </div>
      </transition>
    </BaseDelay>

    <BaseDelay delay="800">
      <transition
        name="fade-in-left"
        appear>
        <div class="right-side">
          <TitleBlock title="接报类型" />
          <div class="legend">
            <div class="legend-item">
              <div
                class="color-block"
                style="background: #00e4ff"></div>
              <span>直接接报</span>
            </div>
            <div class="legend-item">
              <div
                class="color-block"
                style="background: #ffb800"></div>
              <span>需调度接报</span>
            </div>
          </div>
          <TypeBlock
            v-for="item in eventTypeStats"
            :key="item.eventType"
            :data="item" />
        </div>
      </transition>
    </BaseDelay>
  </div>
</template>

<script>
import TypeBlock from './TypeBlock'
import DataBlock from './DataBlock'
import { getEventDataSummary } from '@/api/event'
import { getAndSetDict } from '@/utils'
export default {
  components: {
    TypeBlock,
    DataBlock,
  },
  props: {
    activeArea: {
      type: String,
    },
  },
  inject: ['dateRange'],
  data() {
    return {
      disasterEventStats: {},
      eventTypeStats: {},
      totalEvents: 0,
      totalCalls: 0,

      // dict
      RESCUE_EVENT: {},
      event_level: {},
    }
  },
  computed: {},
  watch: {
    activeArea(newVal) {
      this.getEventDataSummary()
    },
  },
  created() {
    getAndSetDict('RESCUE_EVENT,event_level', this)
    this.getEventDataSummary()
  },
  methods: {
    async getEventDataSummary() {
      const payload = {
        areaCode: this.activeArea,
        startDate: this.dateRange[0].replaceAll(/-/g, ''),
        endDate: this.dateRange[1].replaceAll(/-/g, ''),
      }
      const [err, res] = await getEventDataSummary(payload)
      if (err) return
      const { disasterEventStats, eventTypeStats, totalEvents, totalCalls } = res.data
      this.disasterEventStats = (disasterEventStats || []).map((item) => ({
        ...item,
        label: this.event_level[item.disasterLevel],
        processCount: Number(item.totalCount - item.endCount) || 0,
        icon: require(`@/assets/images/level-${item.disasterLevel}-count.png`),
      }))
      this.disasterEventStats.sort((a, b) => b.disasterLevel - a.disasterLevel)
      this.eventTypeStats = (eventTypeStats || []).map((item) => ({
        ...item,
        label: this.RESCUE_EVENT[item.eventType],
        dispatchCount: Number(item.totalCount - item.directCount) || 0,
      }))
      this.totalEvents = totalEvents
      this.totalCalls = totalCalls
    },
  },
}
</script>

<style lang="scss" scoped>
.event-data-panel {
  @include full;
  @include flex-row;
  position: absolute;
  top: 0;
  left: 0;
  padding: 58px 0 96px;

  .left-side,
  .right-side {
    @include flex-col;
    height: 100%;
  }

  .left-side {
    align-items: flex-start;
    > :nth-child(1) {
      margin-left: 198px;
    }
    > :nth-child(2) {
      margin-left: 132px;
    }
    > :nth-child(3) {
      margin-left: 66px;
    }
    > :nth-child(4) {
      margin-left: 0;
    }
    > :nth-child(5) {
      margin-left: 0;
    }
    > :nth-child(6) {
      margin-left: 50px;
    }
    > :nth-child(7) {
      margin-left: 120px;
    }
  }

  .right-side {
    align-items: flex-start;
    > :nth-child(1) {
      margin-left: 0;
    }
    > :nth-child(2) {
      margin-left: 66px;
    }
    > :nth-child(3) {
      margin-left: 132px;
    }
    > :nth-child(4) {
      margin-left: 198px;
    }
    > :nth-child(5) {
      margin-left: 198px;
    }
    > :nth-child(6) {
      margin-left: 160px;
    }
    > :nth-child(7) {
      margin-left: 80px;
    }
    .legend {
      @include flex-row;
      width: 220px;

      .legend-item {
        @include flex-row;
        justify-content: flex-start;

        .color-block {
          margin-right: 8px;
          width: 16px;
          height: 16px;
          border-radius: 4px;
        }

        span {
          color: $txtColor-reverse;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
