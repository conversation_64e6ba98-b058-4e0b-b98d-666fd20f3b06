<template>
<div :type="item.eqType" class="full-video" :class="type">
    <div  class="full-video-box">
        <div class="full-video-info">
            <span class="full-video-info-typename blaze-text">{{typeName}}</span>
            <span class="full-video-info-name">{{item.eqName}}</span>
            <span class="label"><font style="color: rgba(255, 255, 255, 0.5);">所在位置:</font> {{ item.location }}</span>
        </div>
        <div data-ccbar-webrtc-video="remote" class="webrtc-video-remote"></div>
        <div class="full-video-close">
          <img src="../images/close.svg" alt="关闭" @click="close" />
        </div>
        <div class="full-video-btn">视频上墙</div>
    </div>
</div>
</template>
<script>
export default {
  name: 'InfoWindow',
  props: {
    item: {
      type: Object,
      required: true
    },
    type: {
      type: String
    }
  },
  data() {
    return{}
  },
  mounted() {
    if(this.type != 'mini' ) window.$('.layout_header').hide();
  },
  beforeDestroy() {
    window.$('.layout_header').show();
  },
  methods: {
    close() {
      this.$emit('close');
    }
  },
  computed: {
    typeName() {
      const typeMap = {
        0: "摄像头",
        1: "行动支队",
        2: "无人机"
      };
      return typeMap[this.item.eqType] || "";
    },
  },
}
</script>
<style scoped>
.full-video{
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 3333;
  background-color: #000;

  &.mini{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 1;

    .full-video-info,.full-video-close,.full-video-btn{
     display: none; 
    }
  }

  .webrtc-video-remote {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    video{
      width: 100%;
    height: 100%;
    }
  }
 
  .full-video-btn{
    cursor: pointer;
    width: 108px;
    height: 32px;
    border-radius: 4px;
    text-align: center;
    line-height: 32px;
    position: absolute;
    right: 22px;
    bottom: 32px;
    z-index: 2;
    font-size: 14px;
  }

    &[type="2"] {
        .full-video-info-name {
        background: url(../images/svg-wrj3.svg) no-repeat left center;
        padding-left: 24px;
        }

        .full-video-btn {
            background: linear-gradient(
                180deg,
                rgba(0, 170, 255, 0.05) 0%,
                rgba(0, 170, 255, 0.5) 100%
            );
            box-sizing: border-box;
            border: 1px solid;
            border-image: linear-gradient(
                180deg,
                rgba(0, 170, 255, 0.2) 0%,
                #00aaff 100%
                )
                1;
            span {
                background: url(../images/svg-wrj3.svg) no-repeat left center;
                padding-left: 24px;
            }
            }
    }
    


    &[type="3"] { 
        .full-video-info-name {
        background: url(../images/svg-zhongduan3.svg) no-repeat left center;
        padding-left: 24px;
        }

        .full-video-btn {
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.05) 0%,
        rgba(0, 170, 255, 0.5) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(0, 170, 255, 0.2) 0%,
          #00aaff 100%
        )
        1;
      span {
        background: url(../images/svg-zhongduan3.svg) no-repeat left center;
        padding-left: 24px;
      }

    }
    }
    


    &[type="0"] {
        .full-video-info-name {
        background: url(../images/svg-zhongduan3.svg) no-repeat left center;
        padding-left: 24px;
        }

        .full-video-btn {
      background: linear-gradient(
        180deg,
        rgba(0, 170, 255, 0.05) 0%,
        rgba(0, 170, 255, 0.5) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(0, 170, 255, 0.2) 0%,
          #00aaff 100%
        )
        1;

      span {
        background: url(../images/svg-zhongduan3.svg) no-repeat left center;
        padding-left: 24px;
      }

    }
    }
}
.full-video-info {
    padding: 16px;
    line-height: 40px;
    color: #fff;
    font-size: 20px;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    background-color: rgba(0,0, 0, 0.2);
    span{margin-right: 30px;}
  }

  .full-video-close {
    position: absolute;
    right: 20px;
    top: 20px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    z-index: 3;
    img{vertical-align: top;}
  }
</style>