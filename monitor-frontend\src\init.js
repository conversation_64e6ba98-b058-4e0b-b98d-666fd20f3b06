import Vue from 'vue'
const path = require('path')

// http
import './http/axios'

// style
import './assets/style/common/index.scss'
import 'animate.css'

// permission
import './router/permission.js'

// plugins
import './plugins/element.js'
import './plugins/echarts.js'


// icons
import SvgIcon from './components/SvgIcon'
Vue.component('SvgIcon', SvgIcon)

const iconModules = require.context('./assets/icons', false, /\.svg$/)
iconModules.keys().map(iconModules)

// components
import Card from './components/Card'
import Dialog from './components/Dialog'
import EmptyWrapper from './components/EmptyWrapper'
import Pagination from './components/Pagination'
import SeamlessScroll from './components/SeamlessScroll'
import TitleBlock from './components/TitleBlock'
import Delay from './components/Delay'
import Titlecomponent from './components/Titlecomponent'

Vue.component('BaseCard', Card)
Vue.component('BaseDialog', Dialog)
Vue.component('EmptyWrapper', EmptyWrapper)
Vue.component('Pagination', Pagination)
Vue.component('SeamlessScroll', SeamlessScroll)
Vue.component('TitleBlock', TitleBlock)
Vue.component('BaseDelay', Delay)
Vue.component('Titlecomponent', Titlecomponent)

// directives
const directiveModules = require.context('./directives', false, /\.js$/)
const directives = directiveModules.keys().map((key) => {
  const module = directiveModules(key)
  module.name = path.basename(key, '.js')
  return module
})
directives.forEach(directive => Vue.directive(directive.name, directive.default))

// filters
import * as filters from './filters'
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})