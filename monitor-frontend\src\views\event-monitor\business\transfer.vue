<template>
  <div class="transfer">
    <div class="content">
      <div class="leftpane">
        <div class="title">组织架构</div>
        <el-input
          placeholder="请输入关键字"
          suffix-icon="el-icon-search"
          @keyup.enter.native="searchEnter"
          @input="handleSearchInput"
          v-model="username"
        ></el-input>
        <el-tree
          class="yq-tree"
          ref="treeList"
          :data="treeData"
          :props="props"
          node-key="id"
          :check-strictly="!multiple"
          :show-checkbox="multiple"
          @check="treecheck"
          @node-click="handleNodeClick"
        >
          <!-- <span slot-scope="{ node, data }">
            <el-radio v-if="!multiple && data.leaf" v-model="selectedId" @change="handleRadioChange(data)"></el-radio>
            <el-checkbox v-else v-model="selectedIds" :label="data.id" @change="handleCheckboxChange(data)"></el-checkbox>
            <span>{{ data.name }}</span>
          </span> -->
        </el-tree>
      </div>
      <div class="rightpane">
        <div class="header">
          <div class="title">已选&emsp;{{ selectedList.length }}</div>
          <div style="color: #00ffff; cursor: pointer" @click="clearSelected">
            清空
          </div>
        </div>

        <div class="selected-list">
          <div class="selectedItem" v-for="item in selectedList" :key="item.id">
            <p>{{ item.name }}</p>
            <i
              class="el-icon-close"
              style="cursor: pointer"
              @click="removeItem(item)"
            ></i>
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>

<script>
import { get, post } from "@/http/request";
import organizationData from "../js/organization.json";

export default {
  name: "transfer",
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    type: {
      type: [ String, Number ],
      default: "1",
    },
  },
  data() {
    return {
      props: {
        label: "name",
        children: "children",
        disabled: "disabled",
      },
      selectedList: [],
      treeData: organizationData.departments,
      queryParams: { data: { pageIndex: 1, pageSize: 100, totalRow: 0 } },
      username: "",
      selectedId: "", // 单选模式下的选中ID
      selectedIds: [], // 多选模式下的选中ID数组
      originalTreeData: [],
    };
  },
  mounted() {
    !this.multiple && this.addStyle();
    this.getList();
  },
  destroyed() {
    // 组件销毁时移除样式
    const style = document.getElementById("transfer-tree-style");
    if (style) {
      document.head.removeChild(style);
    }
  },
  methods: {
    async getList() {
      const data = {
        data: { P_DEPT_ID: "4101", TYPE: this.type },
      };

      const [err, res] = await post(
        "/jr-rescuenet/spi/system/user/listOrg",
        data
      );

      if (res) {
        const deptData = this.processUserList(res.data);
        this.treeData = deptData;
        this.originalTreeData = [...res.data];
        
        // 恢复选中状态
        this.$nextTick(() => {
          this.restoreSelectedState();
        });
      }
    },

    // 递归处理用户列表
    processUserList(userList) {
      return userList.map((user) => {
        console.log("user: ", user);

        const node = {
          id: user.TX_MEETING_USER_ID || user.DEPT_ID,
          name: user.USERNAME || user.DEPT_NAME,
          organization: user.DEPT_NAME,
          contacts: {
            phone: user.MOBILE,
          },
          leaf: typeof user.DEPT_LEVEL === "undefined",
          disabled: typeof user.DEPT_LEVEL !== "undefined",
        };

        // 如果用户有子用户列表，递归处理
        if (user.userList && user.userList.length > 0) {
          node.children = this.processUserList(user.userList);
        }

        return node;
      });
    },

    // 恢复选中状态
    restoreSelectedState() {
      if (this.multiple) {
        // 多选模式
        const checkedKeys = this.selectedList.map(item => item.id);
        this.$refs.treeList.setCheckedKeys(checkedKeys);
      } else {
        // 单选模式
        if (this.selectedList.length > 0) {
          this.$refs.treeList.setCheckedKeys([this.selectedList[0].id]);
        }
      }
    },

    handleSearchInput(value) {
      if (!value) {
        // 当输入框清空时，恢复原始树结构
        this.treeData = this.processUserList([...this.originalTreeData]);
        // 恢复选中状态
        this.$nextTick(() => {
          this.restoreSelectedState();
        });
      }
    },


    searchEnter() {
      if (!this.username) {
        return;
      }

      // 递归搜索树结构
      const searchTree = (nodes) => {
        return nodes.filter(node => {
          const isMatch = node.name.toLowerCase().includes(this.username.toLowerCase());
          
          if (node.children && node.children.length > 0) {
            node.children = searchTree(node.children);
            return isMatch || node.children.length > 0;
          }
          
          return isMatch;
        });
      };

      // 从原始数据开始搜索
      this.treeData = searchTree(this.processUserList([...this.originalTreeData]));
      // 恢复选中状态
      this.$nextTick(() => {
        this.restoreSelectedState();
      });
    },
    hanldeChangeCheck(data, a, b) {
      console.log("hanldeChangeCheck: ", data, a, b);
      if (this.multiple) {
        if (this.selectedList.find((item) => item.id === data.id)) {
          this.selectedList = this.selectedList.filter(
            (item) => item.id !== data.id
          );
        } else {
          if (data.children) return;
          this.selectedList.push(data);
        }
      } else {
      }
    },
    treecheck(data, list) {
      console.log("treecheck: ", data, list);

      if (this.multiple) {
        this.handleMultiple(data, list);
      } else {
        this.handleSimple(data, list);
      }
    },
    handleNodeClick(data) {
      console.log("handleNodeClick: ", data);
      if (data.disabled) return;
      if (this.multiple) return;
      if (data.leaf) {
        this.selectedList = [data];
      }
    },
    handleMultiple(data, list) {
      // 如果是父节点被点击
      if (data?.children) {
        // 获取所有子节点
        const childrenKeys = this.getAllLeafKeys(data);
        console.log("childrenKeys: ", childrenKeys);

        const isAllChecked = childrenKeys.every((key) =>
          this.selectedList.some((item) => item.id === key)
        );

        if (isAllChecked) {
          // 如果所有子节点都已选中，则取消所有子节点
          this.selectedList = this.selectedList.filter(
            (item) => !childrenKeys.includes(item.id)
          );
        } else {
          // 否则选中所有未选中的子节点
          const leafNodes = this.getAllLeafNodes(data);
          leafNodes.forEach((node) => {
            if (!this.selectedList.some((item) => item.id === node.id)) {
              this.selectedList.push(node);
            }
          });
        }
        return;
      }

      // 如果是叶子节点被点击
      if (this.selectedList.find((item) => item.id === data.id)) {
        // 取消选中
        this.selectedList = this.selectedList.filter(
          (item) => item.id !== data.id
        );
      } else {
        // 选中节点
        this.selectedList.push(data);
      }

      // 更新父节点的选中状态
      this.$nextTick(() => {
        this.updateParentCheckedStatus(data);
      });
    },
    handleSimple(data, list) {
      // 如果点击的是父节点，不做处理
      if (data.children) {
        this.$refs.treeList.setCheckedKeys(
          this.selectedList.map((item) => item.id)
        );
        return;
      }

      console.log("data: ", data);

      // 如果当前节点被选中
      if (list.checkedKeys.length > 0) {
        // 如果点击的是已选中的节点，则取消选中
        if (this.selectedList.length && this.selectedList[0].id === data.id) {
          this.clearSelected();
        } else {
          // 否则选中当前节点
          this.$refs.treeList.setCheckedKeys([data.id]);
          this.selectedList = [data];
        }
      } else {
        // 取消选中
        this.clearSelected();
      }
    },
    // 获取所有叶子节点的key
    getAllLeafKeys(node) {
      const keys = [];
      const traverse = (node) => {
        if (!node.children) {
          keys.push(node.id);
          return;
        }
        node.children.forEach((child) => traverse(child));
      };
      traverse(node);
      return keys;
    },
    // 获取所有叶子节点
    getAllLeafNodes(node) {
      const nodes = [];
      const traverse = (node) => {
        if (!node.children) {
          nodes.push(node);
          return;
        }
        node.children.forEach((child) => traverse(child));
      };
      traverse(node);
      return nodes;
    },
    // 更新父节点的选中状态
    updateParentCheckedStatus(node) {
      const parentKey = this.getParentKey(node.id);
      if (!parentKey) return;

      const parent = this.findNodeById(this.treeData, parentKey);
      if (!parent) return;

      const childrenKeys = this.getAllLeafKeys(parent);
      const checkedChildren = childrenKeys.filter((key) =>
        this.selectedList.some((item) => item.id === key)
      );

      if (checkedChildren.length === 0) {
        this.$refs.treeList.setChecked(parentKey, false);
      } else if (checkedChildren.length === childrenKeys.length) {
        this.$refs.treeList.setChecked(parentKey, true);
      } else {
        this.$refs.treeList.setChecked(parentKey, true);
      }
    },
    // 获取父节点的key
    getParentKey(id) {
      let parentKey = null;
      const traverse = (node, parent) => {
        if (node.id === id) {
          parentKey = parent?.id;
          return true;
        }
        if (node.children) {
          return node.children.some((child) => traverse(child, node));
        }
        return false;
      };
      this.treeData.some((node) => traverse(node));
      return parentKey;
    },
    // 根据id查找节点
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
          const found = this.findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },
    clearSelected() {
      this.selectedList = [];
      this.selectedId = "";
      this.selectedIds = [];
      this.$refs.treeList.setCheckedKeys([]);
    },
    addStyle() {
      // 动态添加样式
      const style = document.createElement("style");
      style.type = "text/css";
      style.id = "transfer-tree-style"; // 添加唯一id
      style.innerHTML = `
        .el-tree .el-tree-node .is-leaf + .el-checkbox .el-checkbox__inner {
          display: inline-block;
        }
        .el-tree .el-tree-node .el-checkbox .el-checkbox__inner {
          display: none;
        }
        ${
          !this.multiple
            ? `
          .el-tree .el-tree-node.is-leaf .el-checkbox {
            display: none;
          }
          .el-tree .el-tree-node:not(.is-leaf) .el-checkbox {
            display: inline-block;
          }
        `
            : ""
        }
      `;
      document.head.appendChild(style);
    },
    close() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit", this.selectedList);
    },
    removeItem(item) {
      this.selectedList = this.selectedList.filter((i) => i.id !== item.id);
      if (this.multiple) {
        this.selectedIds = this.selectedIds.filter((id) => id !== item.id);
      } else {
        this.selectedId = "";
      }
      this.$refs.treeList.setCheckedKeys([]);
    },
    handleRadioChange(data) {
      if (data.children) {
        // 如果是父节点，取消选中
        this.selectedId = "";
        return;
      }
      this.selectedList = [data];
    },
    handleCheckboxChange(data) {
      if (data.children) {
        // 如果是父节点，处理所有子节点
        const leafNodes = this.getAllLeafNodes(data);
        if (this.selectedIds.includes(data.id)) {
          // 选中所有子节点
          leafNodes.forEach((node) => {
            if (!this.selectedIds.includes(node.id)) {
              this.selectedIds.push(node.id);
            }
          });
        } else {
          // 取消选中所有子节点
          leafNodes.forEach((node) => {
            this.selectedIds = this.selectedIds.filter((id) => id !== node.id);
          });
        }
      }

      // 更新选中列表
      this.selectedList = this.treeData
        .flatMap((node) => this.getAllLeafNodes(node))
        .filter((node) => this.selectedIds.includes(node.id));
    },
  },
};
</script>

<style lang="scss" scoped>
.transfer {
  @include full;
  border-radius: 0px 4px 4px 0px;
  font-style: 14px;
  font-family: Alibaba PuHuiTi 3;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: #ffffff;

  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.content {
  flex: 1;
  display: flex;
  background: #0080ff10;
  min-height: 0; // 防止溢出
}

.footer {
  line-height: 40px;
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 0;
  flex-shrink: 0;
}

.leftpane {
  height: 100%;
  width: 50%;
  position: relative;
  padding: 16px;
  gap: 16px;
  overflow-y: auto;

  display: flex;
  flex-direction: column;

  &:after {
    display: block;
    position: absolute;
    right: 0;
    top: 0;
    content: "";
    width: 1px;
    height: 100%;
    background-color: #ffffff10;
  }

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ffffff20;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #ffffff10;
    border-radius: 3px;
  }
}

.rightpane {
  width: 50%;
  display: flex;
  flex-direction: column;
  min-height: 0; // 防止溢出

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    flex-shrink: 0;
  }

  .selected-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ffffff20;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #ffffff10;
      border-radius: 3px;
    }
  }

  .selectedItem {
    background-color: #0080ff20;
    line-height: 32px;
    padding: 5px 16px;
    box-sizing: border-box;
    width: 100%;
    border-radius: 4px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-info {
      flex: 1;

      .name {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .details {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .contacts {
        font-size: 12px;
        color: #909399;

        span {
          margin-right: 10px;

          &.emergency {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.el-button {
  font-size: 16px;
  width: 112px;
}
</style>
