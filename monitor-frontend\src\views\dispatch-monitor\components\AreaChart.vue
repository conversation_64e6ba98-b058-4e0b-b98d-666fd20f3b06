<template>
  <div class="area-chart y-container no-padding">
    <BaseDelay delay="1000">
      <h3 class="area-chart_title blaze-text">郑州市事件分布</h3>
    </BaseDelay>
    <BaseDelay delay="1000">
      <div class="area-chart_tabs">
        <!-- <el-button v-for="item in tabList" :key="item.value" :type="activeTab === item.value ? 'primary' : 'default'" @click="handleTabClick(item)">{{ item.label }}</el-button> -->
        <div class="searchcion" v-show="activeTab == 'eventDistribution'" @click="opendialog"><img src="@/assets/images/search.svg" alt="" style="font-weight: 600" /></div>
        <div v-for="item in tabList" :key="item.value" :class="[activeTab === item.value ? 'btnclassactive' : 'btnclass']" @click="handleTabClick(item)">{{ item.label }}</div>
      </div>
    </BaseDelay>
    <div class="area-chart_map_background"></div>
    <div class="area-chart_map chart-container y-container no-padding" ref="chart-container" style="position: relative; z-index: 2"></div>
    <!-- 提示 -->
    <div class="eventpoint" v-show="activeTab == 'eventDistribution'">
      <div class="eventpointitem" @click="handleEventPointClick(4)">
        <!-- <img :src="require('@/assets/images/' + 11 + '.png')" alt="" /> -->
        <div class="point1"></div>
        <div class="eventpointitemtext">特大事件</div>
      </div>
      <div class="eventpointitem" @click="handleEventPointClick(3)">
        <!-- <img :src="require('@/assets/images/' + 22 + '.png')" alt="" /> -->
        <div class="point2"></div>
        <div class="eventpointitemtext">重大事件</div>
      </div>
      <div class="eventpointitem" @click="handleEventPointClick(2)">
        <!-- <img :src="require('@/assets/images/' + 33 + '.png')" alt="" /> -->
        <div class="point3"></div>
        <div class="eventpointitemtext">较大事件</div>
      </div>
      <div class="eventpointitem" @click="handleEventPointClick(1)">
        <!-- <img :src="require('@/assets/images/' + 44 + '.png')" alt="" /> -->
        <div class="point4"></div>
        <div class="eventpointitemtext">普通事件</div>
      </div>
    </div>
    <!-- 提示底部 -->
    <!-- <div class="eventpointbottom" v-show="activeTab == 'eventDistribution'">
      <div class="eventpointbottomitem">
        <div class="eventpointbottomitemtext" :class="{ 'blaze-text1': activefilterbar == '0' }" @click="handlefilterbar('0')">全部</div>
        <img src="@/assets/images/activeword.png" alt="" class="activebaritem" v-if="activefilterbar == '0'" />
      </div>
      <div class="eventpointbottomitem">
        <div class="eventpointbottomitemtext" :class="{ 'blaze-text1': activefilterbar == '4' }" @click="handlefilterbar('4')">特大事件</div>
        <img src="@/assets/images/activeword.png" alt="" class="activebaritem" v-if="activefilterbar == '4'" />
      </div>
      <div class="eventpointbottomitem">
        <div class="eventpointbottomitemtext" :class="{ 'blaze-text1': activefilterbar == '3' }" @click="handlefilterbar('3')">重大事件</div>
        <img src="@/assets/images/activeword.png" alt="" class="activebaritem" v-if="activefilterbar == '3'" />
      </div>
      <div class="eventpointbottomitem">
        <div class="eventpointbottomitemtext" :class="{ 'blaze-text1': activefilterbar == '2' }" @click="handlefilterbar('2')">较大事件</div>
        <img src="@/assets/images/activeword.png" alt="" class="activebaritem" v-if="activefilterbar == '2'" />
      </div>
      <div class="eventpointbottomitem">
        <div class="eventpointbottomitemtext" :class="{ 'blaze-text1': activefilterbar == '1' }" @click="handlefilterbar('1')">普通事件</div>
        <img src="@/assets/images/activeword.png" alt="" class="activebaritem" v-if="activefilterbar == '1'" />
      </div>
    </div> -->
    <!-- 提示top -->
    <div class="showevent" v-if="eventOverviewdata.length >= 3">
      <div class="showeventitem" v-for="(item, index) in eventOverviewdata.slice(0, 3)" :key="index">
        <img :src="require('@/assets/images/showeventicon.png')" alt="" class="showeventicon" />
        <div class="showeventitempart">
          <div>{{ item.areaName }}</div>
          <div class="textcount">
            <span class="wordtext">在办数</span><span class="color1">{{ item.notEndCount }}</span>
          </div>
          <div class="textcountgray">
            <span>接报数</span><span class="color2">{{ item.totalCount }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="showevent">
      <div class="showeventitem" v-for="(item, index) in eventOverviewdata" :key="index">
        <img :src="require('@/assets/images/showeventicon.png')" alt="" class="showeventicon" />
        <div class="showeventitempart">
          <div>{{ item.areaName }}</div>
          <div class="textcount">
            <span>在办数</span><span class="color1">{{ item.notEndCount }}</span>
          </div>
          <div class="textcountgray">
            <span>接报数</span><span class="color2">{{ item.totalCount }}</span>
          </div>
        </div>
      </div>
    </div> -->
    <BaseDialog :visible.sync="eventDetailVisible" title="事件详情">
      <EventDetail :data="eventDetailData" />
    </BaseDialog>
    <filtersearch :visible.sync="filterVisible" title="搜索">
      <div class="filterclass">
        <el-input v-model="filtertext" placeholder="请输入" class="filterinput"></el-input>
        <div class="btnbox" @click="searchbtn">搜索</div>
        <div class="btnbox" @click="resetbtn">重置</div>
        <!-- <el-button type="primary" @click="searchbtn">搜索</el-button>
        <el-button type="primary" @click="resetbtn">重置</el-button> -->
      </div>
    </filtersearch>
  </div>
</template>

<script>
import { getEventList, getEventCountByArea } from "@/api/event";
import { getAndSetDict } from "@/utils";
import chartMixins from "@/mixins/chartMixins.js";
import areaMap from "@/assets/map/410100.json";
import EventDetail from "./EventDetail";
const areaEventIcon = require("@/assets/images/area-event-icon.png");
const areaEventIconActive = require("@/assets/images/area-event-icon-active.png");
// const areaEventIcon1 = require("@/assets/images/area-event-icon1.png");
// const areaEventIconActive1 = require("@/assets/images/area-event-icon-active1.png");
const areaEventIcon1 = require("@/assets/images/box2.svg");
const areaEventIconActive1 = require("@/assets/images/box1.svg");
const iconList = [require("@/assets/images/event-point-icon-1.png"), require("@/assets/images/event-point-icon-2.png"), require("@/assets/images/event-point-icon-3.png"), require("@/assets/images/event-point-icon-4.png")];
import filtersearch from "./filtersearch";
// 子区域中心点
// const centerData = areaMap.features
//   .map((i) => i.properties)
//   .reduce((acc, cur) => {
//     !acc[cur.name] && (acc[cur.name] = cur.center)
//     return acc
//   }, {})
// const centerData = {
//   410102: [113.53, 34.789077],
//   410103: [113.605422, 34.650936],
//   410104: [113.75, 34.686453],
//   410105: [113.786037, 34.835838],
//   410106: [113.28, 34.808689],
//   410108: [113.61836, 34.9],
//   410122: [114.022521, 34.721976],
//   410181: [112.98283, 34.75218],
//   410182: [113.391523, 34.789077],
//   410183: [113.380616, 34.537846],
//   410184: [113.73967, 34.394219],
//   410185: [113.037768, 34.459939],
// };

export default {
  components: { EventDetail, filtersearch },
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      filtertext: "",
      centerData: {
        410102: [113.53, 34.789077],
        410103: [113.605422, 34.650936],
        410104: [113.75, 34.686453],
        410105: [113.786037, 34.835838],
        410106: [113.28, 34.808689],
        410108: [113.61836, 34.9],
        410122: [114.022521, 34.721976],
        410181: [112.98283, 34.75218],
        410182: [113.391523, 34.789077],
        410183: [113.380616, 34.537846],
        410184: [113.73967, 34.394219],
        410185: [113.037768, 34.459939],
      },
      activeTab: null,
      activeArea: null,
      delayAppear: 1000,

      tabList: [
        {
          label: "事件分布",
          value: "eventDistribution",
          fetch: this.getEventList,
        },
        {
          label: "事件总览",
          value: "eventOverview",
          fetch: this.getEventCountByArea,
        },
      ],

      data: [],
      eventList: [],
      eventAreaList: [],
      tabData1: [],
      activefilterbar: "0",
      filterVisible: false,
      // dict
      event_level: {},
      eventOverviewdata: [],
      eventDetailData: {},
      eventDetailVisible: false,
    };
  },
  computed: {
    option() {
      let series;
      if (this.activeTab === "eventDistribution") {
        series = [
          {
            name: "事件分布",
            type: "effectScatter",
            coordinateSystem: "geo",
            data: this.tabData1,
            symbol(value, params) {
              let level = Number(params.data.level) || 0;
              return "image://" + iconList[level - 1];
            },
            symbolSize(value, params) {
              let level = params.data.level;
              if (level === "1") {
                return [24, 24];
              } else if (level === "2") {
                return [32, 32];
                // return [32, 20];
              } else if (level === "3") {
                return [40, 40];
                // return [40, 26];
              } else if (level === "4") {
                return [48, 48];
                // return [48, 32];
              }
              return 0;
            },
            showEffectOn: "emphasis",
            rippleEffect: {
              color: "rgba(1,255,255, .5)",
              number: 2,
              period: 1,
              scale: 3,
              brushType: "stroke",
            },
            emphasis: {
              scale: 1.2,
            },
            hoverAnimation: true,
            label: {
              normal: {
                show: false,
              },
              emphasis: {
                show: false,
                formatter: "{b}",
                position: "right",
              },
            },
            tooltip: {
              show: true,
              trigger: "item",
              formatter: (params) => {
                let level = params.data.level;
                return `${params.name}` + "<br/>" + `事件等级：${this.event_level[level]}`;
              },
            },
            zlevel: 2,
          },
        ];
      } else if (this.activeTab === "eventOverview") {
        series = [
          //柱状体的主干
          {
            type: "lines",
            zlevel: 5,
            effect: {
              show: false,
              symbolSize: 5, // 图标大小
            },
            lineStyle: {
              width: 12, // 尾迹线条宽度
              color: (params) => {
                if (params.data.coords[2] == this.activeArea) {
                  return new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "rgba(255, 173, 0, 0.2)" },
                    { offset: 1, color: "rgba(255, 255, 255, 1)" },
                  ]);
                } else {
                  return new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "rgba(126, 217, 223, 0.1)" },
                    { offset: 1, color: "rgba(255, 255, 255, 1)" },
                  ]);
                }
              },
              //   new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
              //   { offset: 0, color: "rgba(126, 217, 223, 0)" },
              //   { offset: 1, color: "rgba(255, 255, 255, .8)" },
              // ]),
              opacity: 1, // 尾迹线条透明度
              curveness: 0, // 尾迹线条曲直度
            },
            label: {
              show: 0,
              position: "end",
              formatter: "245",
            },
            silent: true,
            data: this.lineData(),
          },
          // 柱状体的顶部
          {
            type: "scatter",
            emphasis: {
              scale: false,
            },
            coordinateSystem: "geo",
            geoIndex: 0,
            zlevel: 5,
            label: {
              normal: {
                show: true,
                position: "top",
                distance: -39,
                color: "#fff",
                fontSize: 24,
                fontWeight: "bold",
                formatter: (params) => {
                  const { notEndCount, totalCount } = params.data[2] || {};
                  return `${notEndCount}/${totalCount}`;
                },
              },
            },
            itemStyle: {
              color: "#00FFF6",
              opacity: 1,
            },
            tooltip: {
              show: false,
            },
            // symbol: "image://" + areaEventIcon1,
            symbol: (value, params) => {
              if (this.activeArea == params.data[2].name) {
                return `image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAA4CAYAAAA2PDy+AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAA/iSURBVHic7Zy9kiRJVoW/cz0is2p6FhgEdMxW4CXQMENAWmEEJJ5geQVeBA0kBlZadc3gDZCxeQcWZru7KjPjHgR3j/DMyqqunp6dHtbylkXFn0eEpx+/v+5+4UZ/0KTPXYEPke2fbB0l+XPX4UP02RvvCoAvnT93DKBvv/1WP//5zz+pOm177h4X9587rhX6CXSAzwbwBbD9OIbja3u9UFZXnnl1da7sx+3yGi/cO+sknxvkzwJwA1fffPONvv76a9jA0nB8ub+89tz2WrCvgZPD5ovjD51fXj8D+3MB/aMD3MHlHLDCBlAZzq/t+za1/dyOp4tnxVOw12rwFNilbae2Pw7X8mLfj08X1y7LrkB/LoCnH/NjV8AtPAXtua3f74Du2nYH7NvxZfmRs0caOfUS1MPFdmz3r23rvbdv357evHnTz6Pt+7ew/Vm4+CoH+1f82dHzX7PEn5aCMF4uyhQwAUuGSgmImSUnlXlmUQhmSkwkoSh7kkkRE5SdUncxlTtluQ+m+0LcBdMXhXJfiLtC2ZcodyXjrqC5MH1ZmL6cYv5ZZNxPMb0pGfs5yt3k2M8q82xNk5gKUTaOlyq4JlDD2Alq4Nrn3JbLYh8W5emIDycvD4tO70/kw5LH3y0sbxdO3y2c3i3k+4XlccGPJ/Jh4fhuwe8Xju8XlndLLu8ylvfJ8pD4MfP4SLAYnwxHyBP4ZDIhjyQnwieTRzJPhBcTrWaUJxilKnbR/50QBTIRtiIAE2cc7H/d/cUi/uFk/ZXsIOxEhnQtX4uptg6kiAiSKaSdNe3CZSa0l7VTxk4qe6ydVGY59hB7STst8z0Rd0rdBdMdlHup3EHcSdMdqTsRO1HuxHQP5Y2yvJGmL6DcI+1E2aHYgQqiCAXoSp89M+fK9esAEcgFcwI3QaDZzh0RO/u0xzFB3CE/2ssjLA9meQB9Aflg4j0sD9byDuejWR7s5RFNj3Y+4jzAcrDiYPKAdUAcLA7ORUi2vZDIZK+gwT6vbzs1wIKQcAMXwZJIGlrj9G/7v7H5R/AOMLYl0q76o8sWgY3qm3v3iQk0Vw6NCTOLmGVNkmYck6QCZQ+aReyk2GHtQ/MdjjtJe7nspekeYi9iL7QX0x7KvRx3UrkX5R7FvZj30EBGs+o2oSiYIpWZ+v2JlXvXxkmcJ8TJXo6IxTjBC/hk5wHnAeXR5AMsHaj3cHoL+WDlAHA+4Aa08qGWzQb8crDzgHw0eYA82T5B/34eUJ4MJ+PG2fUce6u3GtcG2JlVNiVUOBuQkjCq2Lvgdv34zfSXjvIr2YGUclZgQwnYKVA3FnAC4SrfGsTr8cskkAgF6VCUmdSudYydzCRNrRNoJ6ZdBVE7UWZqx9hDmSv4sRdlL8od1hxqYGv6Qsx/EsxfBdOXgXYiWmNlgh/N6Xfp43+nDr9NL+8sL/ZyAB9MHsxycD0+go92PqLl6Hp8qKDnwcqDnY/13PWZPB0JH5wNSHDtQM+62M9Tw7HhKUXjsMaaAgtXkI1DhIzDeCdL8r/w5VL2/2n7K6xEmULp2jmyA8sGqEO41fVajV/4FWp9TYKAMgFFaIYoUsyYScRUjzU1Tm/nUSUCMQvNWHMQE1FqGVSoHDuJ6U6O+6iSoHI2gHMBnyAfrdO7dD5YPtlewAvyYucRfMI+WXkyecI+Ip9MNq5rnI5Plo/2cqzHebCzSgV7gVyANH4phnKtoVrxzdkT9U2KKpvVrDbJHdwBZHsvwXSM+e9kfyUpTQUXkViuezsbqLHB6vbREebrtb961aRSkQmCTCtywiWBo8iJRY+KKCwUhQpWITUpVECFVChiQiokpWmbIhNQAlXbqnHu5im5e0aZQNokXppBLbt2gASluyiHNLmweCGcxicWL+CjcRJenHkiWNzLexX5fql1RjivXRebGhaVLdSAV9oKkKvxkRVcpclQKDGaRPxtEyENXDW9S2baEWSY+rcBajeT3wbpafX77zqvfL9Wu2D6JFxASZKwSNUIOlQxvkhQyAiicX5SDamggALX6ySjSdEkxKso26/YdtVBcOU6ZzUovbT6N26sHYQgnc0Yt93kXLZ3uWpEGz7gIemacdjeoNaOqgZU76mK1gVsE5Js0UFuldWE+XPkFVgGcAmyAZu41bb2Hm8IDsh5PHmO2g+uNa59xjR7oLuPIl3xqqae6MDW36oeHqL3IDUdaz/TUq8kefHlD5I30ZXeAlWSnUOncLsne32C4W0v0dicWv9tp7hGS7oRFfIapgmRaYdQVas4bFvIE8FOrjXu4rmDG0kDWTaNi6ndwFTOFasF7/MavQboTZnXh5a1o9shbzfOfvn5e9v9S0f98tnn6vCxVwYgVk2l3GBMM5Z4LT2pqQdPqBtWbs1euVWCiqmJBjKgQHIDxZNqx3PncLuKZRq4mbKi6uJucHVAR/G8ulF6CvTzNDTA6IgBuobYCv6ncen3pcFbPKe1j/o1P/pFqj7odqoObtPCXfL3L6qzWFcywuqGhs1UDWbZrjfpnNo4dwV35eDqKnVw108F5vt5Apc/8YMlxDMN/fumH+GrqgZA/VwVx7T27v3HTUfJuKlhEogAp5vCV1gkUwWqi2A72EBOcNUnXdo3gJvYwKxjflF1NXnOiDd6LQ2dJ9pxjh1KQxmv51V6SrXTNwZ083gSNHHOjRVEdYPK6/nqX7TjNlrgFDSYnaOI+jw89v+XhnZLIIYL0RjHvVxnta6pK18bURnUleFlPKlK7BqdGkW02Di2OQw0+R7C6R7dcv/OayyWG12jkTupHJw1CNnsNilkp5/q6NVkaZZYD1+rDjloMhAxuD3VHG99YOPa1kcGcOsHe5gy2SoZ0Sr2e2uRPzyKBkwmyiEmU0OUm0xeBWY3sd1UtGSxBqPcLN/qO42uXBPBXZnXct4Mtw5uFxHpgfOjPpO+0B83+iCtbaZBerZrzXgCRDQp2g3cvh/NzmRzW6dBPGz/u16mHq/cC87R66dya24dpNKVyNaNXkkd3sbFocrVIZwy2YTyaF1rVK2cu63TWZBiM6b6w7Tr1DFgXT7vvAbqJlFu9DFUA3OdffrgAjCqQBHYS5Wjm3l16bXUIag24HjN8h2Al9pgg8Y+wqbux0EHD++70euoR1y3tt3atIPe7Zs2ODgGlKqI9vquEKsvPfWCMYDUC684ZetBdaymuka9QiO4lx3lJqpfR4Mt3M5HYFfurAbtGo6qIHsNXZ/h0IOVsV4cSJwr8aEim6i44PazHrgZCjd6DfX2GiXgZfv63FMZb63h4vPhW2AcU7sE7XpF6kMx6Oxrlb3R96MX2nNo84+iuHRW/RxAz7147HE3cD+dOie/ZKS+CuT68HQ2Lu7+9FODeD3xLYDxOSjPDTDgGv5DBKTGQxwX931R/HUceePeH5YGq/nDRS9KXuAQF7HNG/2BUPeazjnY5x3C16T9te6whUlv9EPQtcAFXG37c4x85nI5G8B+wak507/dir780BaqvIH8qdTb8AKPtc0HE+nZxjb0kfp4seBLL+iki/0N5O9PlwGP84HBD5IunlesIvqVGr3HRbdeprN7N5C/P43g+On1fIaz+5DhKqrFuGOSwCmpzRORUbbYyqiQQ22mgeEsVNlnCF3GoW8gfxw9DfM+4eigtn3fO8eZ4Fotq2xLW0RfHxwXlvYwlGQjBW3wuS55ALwCfeGbPTm/0evouXZTn+HRB4hqo6/eT1CnuV+8p87WS6aqjeW2DGJ87/aM0TgsGF7XyLjFR3tP8xOgb/Rh6hKx0yD9zhafaRXVaiNKlYv15G3r+6Y6KnE+/bwf9cHkjlU0GdznC2WiiwH/p0Df6HV0vcWUbXpGVZtaZyd33dtWAdVFaOssro0my3Wkd4NYbaK11vm41LvraMY2jOVM+qKoys0bxDf6GGrqLjqXbkOGAIq2WDup61OajbSuXbrk4oabptYdzoRE6xXbjHpYJ3oFJi21eVmrmM4roMZNUL+K1rbzOi/rTNBWcNvVupwQ07gWoK1lof11k8rUOVlSncgFa8+wrFU6qy9ttfoUEq8DE6nhbZu157XiN/ogddun593Y2rEBW5VkXwvMyr29K5h11WG2dcN90ewkjBM52kxa1sJPNam7SVbhbHN1W6U0APvTTT/4U6QtU4L6uYYlp+orA5uu7EZWA1ZdpdIkavWLnUJoMpIiXXmf1bcVyGcmVqPGqebCTWr6IfoQ1Q3ij6MmGUepl7WdV/A2kbwynyqoHkCXml4VVCv6nGOFWkSjGuKruq3UFTtsUzbHLrAMuvjawvAbPaXVpx2YQlQXqJ+oiegeAKn2WNWuDezoOripWSxpUjRfKpo27nlbaqFIywrlOkfTm3UtNitbo6Hfgiq3ocjX02oFbwbUOd4N5C6W1Wwlaq6KFn1UCEKy6qw6aaJZZk27KquUzVgtOIMVSV3FpLayqWGogUufrJ5V3Dj4JVo5lG38tocY27XN+1l93WY39/V/FdxYH+3cXCfXeqJp6RaJJEymFHWRP64C2y1cJglZ3Uvr6/4bjSoZeJoe70bnpCuHl/MzNoirAexVUAtGcBWqUYy+KlyG76b+7u5HWYoB5IzaG9rSRG9hjvagL/Ivjl7TTUC/jrT+G8nE5iJthlVTn9R0Oi1fAxXcmpsjMAWhwP8xpYmQ6lrvthTGUkTLu5VtjfAatLhIodR08wby4KDd6BNoAxN6C3dfFxRGoTXFUCDLcsvLqZBQifynafOrIuRMy9Hyc+Aa77Do8eaaAaCHLGMAdlxNeItgfTzlYJCOM2a62xRrGW2J0CrXVkOrJWORVLIC9+vpF6ffTFI1ubGlNnSgcGKFegzLTai3PC99+UqPakVdebg67Ndm4H8aadiJntZvu63zcp9EXcfUfdAz/I265wfuv1sEcP1Grs5Rve4xiAE9M7GcrlnfCNWBPgfov97G4e+BdbgQYHYdA8weiVxHhUVFUy3rS7J600TBsROapdj1NIMt9eBOKjM1r2RPQbgTMVNTFdZyxCx6+ZhBU01vWPb1uHwBuz+W5j/C5Utp/hlRvhDlDazJS++ouSo/IQV2S3FYk4i+t0/v4PSdy/LWefxfOPyPOf7WLO9MHlHPW5l1r+UIHE0ea47LrLktySNrQtLl4MwDUa8785EtH+bRykfqPru32fpAdne2VrUPE9ZIBZBq3P7reTr88qtf8NsGMMIWUk3vVpdxr8vGoQUsBle8OcDVN+tWXaga8QocpWaabVloHe241OSiih1uHcGxQ2UPMaPWSRw7ETVVsGNXM8xObyDuRdxV8DUjlW3CoBPnMuRGWw8YE6b1UZSrx2RLPePm74fd6kjcwXRCLJgiysFeDiJ2Jg9SziYOeDkITVYUewk5K7sJ7LSIdJB2pojFooCXtha/pbMyNbOkWuy56eKITZSre8FVYH6X5t9l//P89ek3Y5/9P+UhXsu7GQIqAAAAAElFTkSuQmCC`;
              }
              return `image://data:image/png;base64,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`;
            },
            symbolSize: (value, params) => {
              if (this.activeArea == params.data[2].name) {
                return [106, 44];
              } else {
                return [106, 44];
              }
            },
            symbolOffset: (value, params) => {
              if (this.activeArea == params.data[2].name) {
                return [0, -48];
              } else {
                return [0, -23];
              }
            },
            z: 999,
            data: this.scatterData(),
          },
          // 柱状体的底部
          {
            geoIndex: 0,
            zlevel: 4,
            type: "effectScatter",
            coordinateSystem: "geo",
            rippleEffect: {
              scale: 15,
              brushType: "fill",
            },
            showEffectOn: "render",
            label: {
              normal: {
                formatter: "{b}",
                position: "bottom",
                color: "#fff",
                fontSize: 16,
                distance: 10,
                show: true,
              },
            },
            symbol: "circle",
            symbolSize: [10, 5],
            itemStyle: {
              color: (params) => {
                if (params.data.code == this.activeArea) {
                  return "rgb(255, 173, 0, 0.5)";
                } else {
                  return "rgb(176, 217, 237, 0.6)";
                }
              },

              opacity: 1,
            },
            tooltip: {
              show: false,
            },
            data: this.scatterData2(),
          },
          // 底部外框
          {
            type: "scatter",
            coordinateSystem: "geo",
            geoIndex: 0,
            zlevel: 4,
            label: {
              show: false,
            },
            symbol: "circle",
            symbolSize: [50, 20],
            itemStyle: {
              color: (p) => {
                if (p.data.code == this.activeArea) {
                  return {
                    type: "radial",
                    x: 0.5,
                    y: 0.5,
                    r: 0.5,
                    colorStops: [
                      {
                        offset: 0,
                        color: "rgb(255, 255, 255, 1)", // 0% 处的颜色
                      },
                      {
                        offset: 0.45,
                        color: "rgb(255, 173, 0, 0.2)", // 100% 处的颜色
                      },
                      {
                        offset: 0.75,
                        color: "rgb(255, 173, 0, 0.4)", // 100% 处的颜色
                      },
                      {
                        offset: 1,
                        color: "rgb(255, 173, 0, 0.1)", // 100% 处的颜色
                      },
                    ],
                    global: false, // 缺省为 false
                  };
                } else {
                  return {
                    type: "radial",
                    x: 0.5,
                    y: 0.5,
                    r: 0.5,
                    colorStops: [
                      {
                        offset: 0,
                        color: "rgb(255, 255, 255, 1)", // 0% 处的颜色
                      },
                      {
                        offset: 0.45,
                        color: "rgb(255, 255, 255, 0.2)", // 100% 处的颜色
                      },
                      {
                        offset: 0.75,
                        color: "rgb(255, 255, 255,0.4)", // 100% 处的颜色
                      },
                      {
                        offset: 1,
                        color: "rgb(0, 170, 255,0.1)", // 100% 处的颜色
                      },
                    ],
                    global: false, // 缺省为 false
                  };
                }
              },
              // {
              //   type: "radial",
              //   x: 0.5,
              //   y: 0.5,
              //   r: 0.5,
              //   colorStops: [
              //     {
              //       offset: 0,
              //       color: "rgb(255, 255, 255, 1)", // 0% 处的颜色
              //     },
              //     {
              //       offset: 0.45,
              //       color: "rgb(255, 255, 255, 0.2)", // 100% 处的颜色
              //     },
              //     {
              //       offset: 0.75,
              //       color: "rgb(255, 255, 255,0.4)", // 100% 处的颜色
              //     },
              //     {
              //       offset: 1,
              //       color: "rgb(0, 170, 255,0.1)", // 100% 处的颜色
              //     },
              //   ],
              //   global: false, // 缺省为 false
              // },
              opacity: 1,
            },
            silent: true,
            data: this.scatterData2(),
          },

          // {
          //   name: "事件总览",
          //   type: "scatter",
          //   coordinateSystem: "geo",
          //   data: this.data,
          //   symbol: (value, params) => {
          //     if (this.activeArea === params.data.name) {
          //       return "image://" + areaEventIconActive;
          //     }
          //     return "image://" + areaEventIcon;
          //   },
          //   symbolKeepAspect: true,
          //   symbolSize: (value, params) => {
          //     if (this.activeArea === params.data.name) {
          //       return 190;
          //     }
          //     return 160;
          //   },
          //   symbolOffset: [0, -20],
          //   emphasis: {
          //     scale: 1.15,
          //   },
          //   hoverAnimation: true,
          //   label: {
          //     normal: {
          //       show: true,
          //       position: "top",
          //       distance: -53,
          //       color: "#fff",
          //       fontSize: 24,
          //       fontWeight: "bold",
          //       fontStyle: "italic",
          //       formatter: (params) => {
          //         const { notEndCount, totalCount } = params.data || {};
          //         return `${notEndCount}/${totalCount}`;
          //       },
          //     },
          //   },
          //   itemStyle: {
          //     opacity: 1,
          //   },
          //   tooltip: {
          //     show: false,
          //   },
          //   zlevel: 2,
          // },
          // {
          //   name: "label-area",
          //   type: "scatter",
          //   coordinateSystem: "geo",
          //   data: this.data,
          //   symbolSize: 0,
          //   label: {
          //     normal: {
          //       show: true,
          //       fontSize: 16,
          //       // fontWeight: "bold",
          //       color: "#fff",
          //       offset: [0, 60],
          //       formatter: (params) => {
          //         const { areaName } = params.data || {};
          //         return areaName;
          //       },
          //     },
          //   },
          //   itemStyle: {
          //     opacity: 1,
          //   },
          //   tooltip: {
          //     show: false,
          //   },
          //   zlevel: 3,
          // },
        ];
      }
      return {
        tooltip: {},
        geo: {
          show: false,
          map: "areaMap",
          roam: false, //不开启缩放和平移
          silent: true,
          zoom: 1,
          // top: "310",
          // left: "38",
          // right: "38",
          // aspectScale: 1.3,
          // zlevel: 1,
          top: "37.5%",
          left: "4.6%",
          right: "4.6%",
          bottom: "26%",
          aspectScale: 1.2, // 根据背景图宽高比(990/698≈1.418)
        },
        series,
      };
    },
  },
  watch: {
    chart(chart) {
      if (chart) {
        chart.on("click", this.switchHighlight);
      }
    },
    activeArea(activeArea) {
      this.$emit("changeActive", activeArea);
      this.chart && this.chart.setOption(this.option, true);
    },
    tabData1() {
      if (this.activeTab === "eventDistribution") {
        this.chart && this.chart.setOption(this.option, true);
      }
    },
  },
  created() {
    getAndSetDict("event_level", this);
    this.handleTabClick(this.tabList[1]);
    this.$echarts.registerMap("areaMap", areaMap);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.off("click", this.switchHighlight);
    }
  },
  methods: {
    searchbtn() {
      this.tabData1 = this.data.filter((i) => {
        return i.name.includes(this.filtertext);
      });
      this.filterVisible = false;
    },
    resetbtn() {
      this.filtertext = "";
      this.tabData1 = JSON.parse(JSON.stringify(this.data));
    },
    handleClose() {
      this.filterVisible = false;
    },
    opendialog() {
      this.filterVisible = true;
    },
    lineMaxHeight() {
      return 0.05;
    },
    // 柱状体的主干
    lineData() {
      return this.data.map((item) => {
        if (item.name == this.activeArea) {
          return {
            coords: [this.centerData[item.name], [this.centerData[item.name][0], this.centerData[item.name][1] + 1 * 0.11], item.name],
          };
        } else {
          return {
            coords: [this.centerData[item.name], [this.centerData[item.name][0], this.centerData[item.name][1] + 1 * 0.05], item.name],
          };
        }
      });
    },
    // 柱状体的顶部
    scatterData() {
      return this.data.map((item) => {
        return [this.centerData[item.name][0], this.centerData[item.name][1] + 1 * this.lineMaxHeight(), item];
      });
    },
    // 柱状体的底部
    scatterData2() {
      return this.data.map((item) => {
        return {
          name: item.areaName,
          value: this.centerData[item.name],
          code: item.name,
        };
      });
    },
    handlefilterbar(active) {
      this.activefilterbar = active;
      this.handleEventPointClick(active);
    },
    handleEventPointClick(level) {
      if (level == "0") {
        this.tabData1 = JSON.parse(JSON.stringify(this.data));
      } else {
        this.tabData1 = this.data.filter((i) => {
          return i.level == level;
        });
      }
    },
    handleDetail(row) {
      this.eventDetailData = row;
      this.$nextTick(() => {
        this.eventDetailVisible = true;
      });
    },
    async fetch() {
      let find = this.tabList.find((item) => item.value === this.activeTab);
      if (find && typeof find.fetch === "function") {
        await find.fetch();
      }
    },
    async getEventList() {
      const payload = {
        provinceCode: "410100",
        pageNum: 1,
        pageSize: 999,
        startTime: this.dateRange[0] + " 00:00:00",
        endTime: this.dateRange[1] + " 23:59:59",
      };
      const [err, res] = await getEventList(payload);
      if (err) return;
      this.eventList = res.data?.records || [];
    },
    async getEventCountByArea() {
      const payload = {
        startDate: this.dateRange[0].replaceAll(/-/g, ""),
        endDate: this.dateRange[1].replaceAll(/-/g, ""),
      };
      const [err, res] = await getEventCountByArea(payload);
      if (err) return;
      this.eventAreaList = res.data?.areaEventStats || [];
    },
    async handleTabClick(item) {
      this.activeTab = item.value;
      await this.fetch();
      this.data = this.getData();
      this.tabData1 = this.getData();
      if (this.activeTab === "eventOverview") {
        this.eventOverviewdata = this.getData();
      }
      console.log(this.data);
    },
    getData() {
      if (this.activeTab === "eventDistribution") {
        return this.eventList
          .filter((item) => item.disasterAreaCode && item.disasterAreaCode.startsWith("4101"))
          .map((item) => ({
            ...item,
            name: item.disasterEventName,
            level: item.disasterLevel,
            value: [Number(item.disasterLon), Number(item.disasterLat)],
          }));
      } else if (this.activeTab === "eventOverview") {
        return this.eventAreaList.map((item) => {
          let geoCoord = this.centerData[item.areaCode];
          if (geoCoord) {
            return {
              ...item,
              name: item.areaCode,
              value: geoCoord,
              notEndCount: Number(item.totalCount - item.endCount) || 0,
            };
          } else {
            return null;
          }
        });
      }
      return [];
    },
    switchHighlight(params) {
      console.log("switchHighlight", params);
      if (this.activeTab !== "eventOverview") return;
      if (this.activeArea === params.data[2].name) {
        this.turnDown(params);
      } else {
        this.turnOn(params);
      }
    },
    turnDown(params) {
      console.log("turnDown", params);
      let { seriesIndex, dataIndex } = params;
      this.chart.dispatchAction({
        type: "downplay",
        dataIndex,
      });
      this.activeArea = null;
    },
    turnOn(params) {
      console.log("turnOn", params);
      let { seriesIndex, dataIndex } = params;
      this.data.forEach((v, i) => {
        this.chart.dispatchAction({
          type: "downplay",
        });
      });
      this.chart.dispatchAction({
        type: "highlight",
        dataIndex,
      });
      this.activeArea = params.data[2].name;
    },
  },
};
</script>

<style lang="scss" scoped>
.area-chart {
  position: relative;
  overflow: visible;
  background: url("@/assets/images/area-chart-bg.png") no-repeat center bottom/100% 62.5%;
  width: 1130px;
  height: 676px;
  .area-chart_title {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    font-weight: bold;
  }

  .area-chart_tabs {
    display: flex;
    align-items: center;
    gap: 16px;
    position: absolute;
    // top: -265px;
    top: 18px;
    right: 120px;
    z-index: 11;
  }

  .area-chart_map_background {
    position: absolute;
    top: 236px;
    left: 50%;
    transform: translate(-50%); // rotate(22deg)
    z-index: 1;
    width: 980px;
    height: 698px;
    // width: 100%;
    // height: 100%;
    background: url("@/assets/images/area-chart-map-bg.png") no-repeat center center/100% 100%;
  }
  .area-chart_map {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%); // rotate(22deg)
    width: 990px;
    height: 698px;
    // width: 100%;
    // height: 100%;
  }
}
.btnclass {
  font-size: 16px;
  height: 40px;
  width: 112px;
  line-height: 40px;
  text-align: center;
  background: url("@/assets/images/btndefault.png") no-repeat center center;
  background-size: 100% 100%;
  color: #ffffff;
  cursor: pointer;
}
.btnclassactive {
  font-size: 16px;
  font-weight: 600;
  height: 40px;
  width: 112px;
  line-height: 40px;
  text-align: center;
  background: url("@/assets/images/btnactive.png") no-repeat center center;
  background-size: 100% calc(100% + 6px);
  color: #ffffff;
  cursor: pointer;
}
.eventpoint {
  z-index: 999;
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  height: 24px;
  color: #ffffff;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 32px;
  .eventpointitem {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
.showevent {
  //
  position: absolute;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 708px;
  height: 82px;
  display: flex;
  align-items: center;
  gap: 24px;
  .showeventitem {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    gap: 8px;
    height: 102px;
    width: 220px;
    background: url("@/assets/images/showeventbg.png") no-repeat center bottom/100% 60%;
    .showeventitempart {
      font-size: 16px;
      margin-top: -10px;
      .textcount {
        font-size: 12px;
        color: #ffffff;
      }
      .textcountgray {
        font-size: 12px;
        color: #ffffff80;
      }
    }
  }
}
.color1 {
  font-size: 24px;
  font-weight: bold;
  color: #00ffff;
  margin-left: 8px;
}
.color2 {
  font-size: 16px;
  font-weight: bold;
  color: #00aaff;
  margin-left: 8px;
}
.wordtext {
  font-size: 12px;
}
.showeventicon {
  width: 62px;
  height: 62px;
}
.searchcion {
  width: 40px;
  height: 40px;
  background-color: #0269bd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: -12px;
}
.eventpointbottom {
  z-index: 999;
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: url("@/assets/images/eventpointbottombg.png") no-repeat center center/100% 100%;
  width: 1028px;
  height: 52px;
  line-height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  .eventpointbottomitem {
    z-index: 999;
    cursor: pointer;
    height: 52px;
    line-height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .eventpointbottomitemtext {
      z-index: 999;
      font-size: 16px;
      color: #74c5dc;
    }
    .activebaritem {
      position: absolute;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
    }
  }
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 600;
}
.filterclass {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;
  gap: 16px;
  .filterinput {
    width: 350px;
  }
}
.btnbox {
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  height: 40px;
  background: #054598;
  border-radius: 4px;
  cursor: pointer;
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.point1 {
  /* 数据图表色/朱砂 */
  background: #dc0055;
  box-shadow: 0px 0px 5px 0px #dc0055;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}
.point2 {
  background: #f2884a;
  box-shadow: 0px 0px 4px 0px #f2884a;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}
.point3 {
  background: #ffdc00;
  box-shadow: 0px 0px 3px 0px #f0ea0c;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}
.point4 {
  background: #00ffff;
  box-shadow: 0px 0px 8px 0px #00ffff;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}
canvas {
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}
</style>
