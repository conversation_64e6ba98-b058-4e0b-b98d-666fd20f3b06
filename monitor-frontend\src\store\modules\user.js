import { getUserInfo, getVideoConferenceCookie, videoConferenceHeartbeat } from '@/api/auth'

export default {
  namespaced: true,
  state: () => ({
    userInfo: null,
    videoCookie: null,
  }),
  mutations: {
    SET_USERINFO(state, userInfo) {
      state.userInfo = userInfo
    },
    SET_VIDEO_CONFERENCE_COOKIE(state, cookie) {
      state.videoCookie = cookie
    },
  },
  actions: {
    async getUserInfo({ commit }) {
      const [err, res] = await getUserInfo()
      if (res) {
        commit('SET_USERINFO', res)
      }
      return [err, res]
    },

    /**
     * 获取云视讯视频会议 Cookie
     */
    async remoteVideoConferenceCookie({ commit, dispatch }) {
      try {
        const [err, res] = await getVideoConferenceCookie()
        if (res) {
          commit('SET_VIDEO_CONFERENCE_COOKIE', res)
          // 轮询视频会议心跳，每3分钟执行一次
          setInterval(() => {
            dispatch('remoteVideoConferenceHeartbeat')
          }, 3 * 60 * 1000)
        }
        return [err, res]
      } catch (err) {
        return [err, null]
      }
    },

    /**
     * 视频会议心跳
     */
    async remoteVideoConferenceHeartbeat({ state, commit }) {
      try {
        const [err, res] = await videoConferenceHeartbeat(state.videoCookie)
        if (res) {
          commit('SET_VIDEO_CONFERENCE_COOKIE', res)
        }
        return [err, res]
      } catch (error) {
        return [error, null]
      }
    },
  },
}