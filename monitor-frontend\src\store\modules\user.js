import { getUserInfo } from '@/api/auth'

export default {
  namespaced: true,
  state: () => ({
    userInfo: null,
  }),
  mutations: {
    SET_USERINFO(state, userInfo) {
      state.userInfo = userInfo
    },
  },
  actions: {
    async getUserInfo({ commit }) {
      const [err, res] = await getUserInfo()
      if (res) {
        commit('SET_USERINFO', res)
      }
      return [err, res]
    },
  },
}