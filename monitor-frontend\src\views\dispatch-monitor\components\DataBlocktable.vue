<template>
  <div class="maincontent">
    <div class="eventtop">
      <div class="eventitem">
        <img src="@/assets/images/eventreceive.png" alt="" class="imgevent" />
        <div class="eventtext">
          <div class="eventtextitem">接报总量</div>
          <div class="eventtextitem1">{{ totalCalls }}</div>
        </div>
      </div>
      <div class="eventitem">
        <img src="@/assets/images/eventtotal.png" alt="" class="imgevent" />
        <div class="eventtext">
          <div class="eventtextitem">事件总量</div>
          <div class="eventtextitem2">{{ totalEvents }}</div>
        </div>
      </div>
    </div>
    <div class="tableheight">
      <el-table :data="disasterEventStats" height="100%" fit ref="table" stripe>
        <el-table-column prop="label" label="事件等级" show-overflow-tooltip min-width="60%">
          <template slot-scope="scope">
            <div class="bar">
              <!-- 显示等级文本 -->
              <div class="level-label">{{ scope.row.label }}</div>
              <div class="progress-container">
                <!-- 相对定位容器 -->
                <!-- 自定义进度条 -->
                <el-progress :percentage="getPercentage(scope.row.endCount)" :stroke-width="8" class="custom-progress" :show-text="false" />
                <!-- 动态指示块 -->
                <div class="custom-indicator" :style="{ left: calcPosition(scope.row.endCount) }"></div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="endCount" label="已完成" min-width="20%">
          <template slot-scope="scope">
            <div class="color1">
              {{ scope.row.endCount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="processCount" label="在办量" min-width="20%">
          <template slot-scope="scope">
            <div class="color2">
              {{ scope.row.processCount }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { initRoll, cancelRoll } from "@/utils/table-roll";

export default {
  name: "DataBlocktable",
  props: {
    disasterEventStats: {
      type: Array,
      default: () => [],
    },
    totalCalls: {
      type: Number,
      default: () => 0,
    },
    totalEvents: {
      type: Number,
      default: () => 0,
    },
  },
  watch: {
    "disasterEventStats.length": {
      handler(length) {
        if (length > 4) {
          this.$nextTick(() => {
            initRoll(this.$refs.table);
          });
        }
      },
      immediate: true,
    },
  },
  beforeDestroy() {
    cancelRoll(this.$refs.table);
  },
  data() {
    return {};
  },
  computed: {
    maxValue() {
      // 处理空数组情况
      if (this.disasterEventStats.length === 0) return 0;
      const values = this.disasterEventStats.map((i) => Number(i.endCount));
      return Math.max(...values) || 0;
    },

    maxProgress() {
      const max = this.maxValue;
      if (max <= 0) return 100; // 默认最大值

      // 优化基数计算逻辑
      const exponent = Math.floor(Math.log10(max));
      const base = Math.pow(10, exponent);
      return max < base ? base : Math.ceil(max / base) * base;
    },
  },
  methods: {
    // 进度条相关
    // getPercentage(value) {
    //   return (value / this.maxProgress) * 100; // 根据动态的最大值计算进度
    // },
    getPercentage(value) {
      if (this.maxProgress <= 0) return 0; // 防止除零
      const percentage = (value / this.maxProgress) * 100;
      // 确保返回合法数值
      return Math.min(Math.max(Number(percentage.toFixed(2)), 100), 0);
    },
    // 计算指示块位置
    calcPosition(percent) {
      let p = (percent / this.maxProgress) * 100;
      // 解决边界溢出问题（当100%时防止溢出）
      const safePercent = Math.min(Math.max(p, 0), 100);
      return `calc(${safePercent}% - 2px)`; // 微调偏移量
    },
  },
};
</script>

<style lang="scss" scoped>
.maincontent {
  margin-bottom: 12px;
  width: 100%;
  .eventtop {
    display: flex;
    align-items: center;
    gap: 16px;
    margin: 16px 0 12px;
    .imgevent {
      width: 56px;
      height: 100%;
    }
    .eventitem {
      padding-left: 16px;
      width: 204px;
      height: 72px;
      border-radius: 4px;
      opacity: 1;
      background: linear-gradient(180deg, rgba(0, 128, 255, 0.1) 0%, rgba(0, 128, 255, 0) 100%);
      display: flex;
      align-items: center;
      gap: 8px;
      .eventtext {
        .eventtextitem {
          font-size: 14px;
          color: #ffffff;
          margin-bottom: 8px;
          margin-top: 16px;
        }
        .eventtextitem1 {
          color: #00ffff;
          font-size: 24px;
          font-weight: bold;
        }
        .eventtextitem2 {
          color: #00aaff;
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }
}
.color1 {
  font-size: 16px;
  font-weight: bold;
  color: #00aaff;
}
.color2 {
  font-size: 16px;
  font-weight: bold;
  color: #00ffff;
}
.bar {
  display: flex;
  align-items: center;
  gap: 16px;
}
.progress-container {
  position: relative;
  width: 100%;
}

/* 进度条样式 */
.custom-progress {
  position: absolute;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);

  ::v-deep .el-progress-bar__outer {
    /* 穿透样式 */
    background-color: #04225f !important;
  }

  ::v-deep .el-progress-bar__inner {
    background-image: linear-gradient(90deg, #074090 0%, #036cbb 50%, #009df0 100%) !important;
    border-radius: 0 100px 100px 0;
  }
}

/* 指示块样式 */
.custom-indicator {
  position: absolute;
  width: 4px;
  height: 14px;
  background: linear-gradient(to bottom, #fff 0%, #20b4ff 100%);
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
  top: 50%;
  transform: translate(-1px, -50%); /* 微调居中 */
  z-index: 2;
  transition: left 0.3s ease;
}

/* 等级文本 */
.level-label {
  color: #fff; /* 根据背景色调整 */
  font-size: 14px;
}
.tableheight {
  height: 220px;
}
</style>
