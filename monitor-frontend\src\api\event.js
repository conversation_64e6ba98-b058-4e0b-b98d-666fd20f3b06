import { get, post } from '@/http/request'

/**
 * 获取事件列表
 * @param {Object} data 查询参数
 * @returns {Promise} 返回事件列表请求的Promise对象
 */
export function getEventList(data) {
  return post('/jr-rescuenet/api/event/management/searchEvent', data)
}

/**
 * 获取区域地图数据
 * @param {Object} data 查询参数
 * @returns {Promise} 返回区域地图数据请求的Promise对象
 */
export function getEventCountByArea(data) {
  return post('/jr-rescuenet/api/strategy/monitor/getAreaMapData', data)
}

/**
 * 获取事件数据总览
 * @param {Object} data 查询参数
 * @returns {Promise} 返回事件数据总览请求的Promise对象
 */
export function getEventDataSummary(data) {
  return post('/jr-rescuenet/api/strategy/monitor/getAreaDataSummary', data)
}
