@use 'sass:math';
@import './variables.module.scss';

// px to vm
@function vw($px) {
  @return math.div($px, $base_width)*100vw;
}

// px to vh
@function vh($px) {
  @return math.div($px, $base_height)*100vh;
}


@mixin full {
  width: 100%;
  height: 100%;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-row {
  @include flex;
  flex-direction: row;
}

@mixin flex-col {
  @include flex;
  flex-direction: column;
}

@mixin container($v-padding: 24px, $h-padding: 24px) {
  @include flex-col;
  justify-content: flex-start;
  align-items: flex-start;
  flex: 1;
  padding: $v-padding $h-padding;
  width: 100%;
  height: 100%;
}

@mixin title {
  // width: max-content;
  font-size: 16px;
  font-weight: 600;
  // white-space: nowrap;
}

@mixin bar($v-padding: 12px, $h-padding: 24px) {
  @include flex-row;
  flex-wrap: wrap;
  padding: $v-padding $h-padding;
  width: 100%;
}

@mixin text-overflow($line) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  word-break: break-all;
}
