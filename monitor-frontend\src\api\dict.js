import { get, post } from '@/http/request'

/**
 * 获取字典数据
 * @param {String|Array} dictNames 字典名称，可以是单个字符串或字符串数组
 * @returns {Promise} 返回字典数据请求的Promise对象
 */
export function getDict(dictNames) {
  return post(`/jr-rescuenet/spi/core/dict/${dictNames.length > 1 ? 'dictItems' : 'dictItem'}/${dictNames}`)  
}

export function getAreaList(data) {
  return post('/jr-rescuenet/api/equipment/getAreaCityDistrict', data)  
}