$base_width: 1920px;
$base_height: 1080px;

// 系列样式前缀
$namespace: y;

// 主题色
$themeColor: #001B57;
$themeColor-dark: #70bcc1; //focus - 深色版本，降低亮度
$themeColor-light: #56E5Ef; //hover - 浅色版本，提高亮度

// 背景颜色
$bgColor: #000;
// $bgColor-dark: rgba(112, 188, 193, 0.1);
$bgColor-dark: rgba(0, 128, 255, 0.2);
// $bgColor-dark--hover: rgba(112, 188, 193, 0.2);
$bgColor-dark--hover: rgba(0, 128, 255, 0.1);

$bgColor-light: rgba(0, 128, 255, 0.05);

// 边框颜色
$borderColor: $bgColor-dark;

// 字体颜色
$txtColor: #fff;
$txtColor-light: #00AAFF;
$txtColor-slight: #CDCDCD;
$txtColor-reverse: #fff;

// 提示颜色
$color-success: #01da0e;
$color-warning: #FBAC06;
$color-danger: #F94742;
$color-info: #909399;
$color-delay: #22B8CF;


:export {
  themeColor: $themeColor;
  namespace: $namespace;
}