<template>
  <div class="event-list y-container--tight no-padding">
    <el-form
      class="search-form"
      ref="searchForm"
      :model="searchForm"
      :inline="true"
      size="small"
    >
      <el-form-item label="级别" prop="disasterLevel">
        <el-select v-model="searchForm.disasterLevel" placeholder="请选择">
          <el-option
            v-for="(label, value) in event_level"
            :key="value"
            :label="label"
            :value="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域" prop="disasterAreaCode">
        <el-select v-model="searchForm.disasterAreaCode" placeholder="请选择">
          <el-option
            v-for="(label, value) in areaDict"
            :key="value"
            :label="label"
            :value="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关键字" prop="disasterEventName">
        <el-input
          v-model="searchForm.disasterEventName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item class="search-form_btn">
        <el-button icon="el-icon-refresh" @click.native="handleReset"
          >重置</el-button
        >
        <el-button type="primary" icon="el-icon-search" @click.native="fetch"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      stripe
      height="100%"
      fit
      ref="table"
      style="width: 100%"
    >
      <el-table-column
        prop="disasterEventName"
        label="名称"
        min-width="300"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column prop="disasterLevel" label="级别" min-width="100">
        <template slot-scope="scope">
          <div :class="['y-sign', event_level_status[scope.row.disasterLevel]]">
            {{ event_level[scope.row.disasterLevel] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="disasterAreaCode" label="区域" min-width="100">
        <template slot-scope="scope">
          {{ areaDict[scope.row.disasterAreaCode] }}
        </template>
      </el-table-column>
      <el-table-column prop="enableStatus" label="状态" min-width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.enableStatus === 'Y' ? 'success' : 'warning'"
            size="small"
          >
            {{ enable_status[scope.row.enableStatus] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="50">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="handleDetail(scope.row)"
            >详情</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <BaseDialog :visible.sync="eventDetailVisible" title="事件详情">
      <EventDetail :data="eventDetailData" />
    </BaseDialog>
  </div>
</template>

<script>
import EventDetail from "./EventDetail";
import { getEventList } from "@/api/event";
import { getAreaList } from "@/api/dict";
import { getAndSetDict, event_level_status } from "@/utils";
import { initRoll, cancelRoll } from "@/utils/table-roll";

export default {
  components: {
    EventDetail,
  },
  inject: ["dateRange"],
  data() {
    return {
      searchForm: {
        disasterEventName: "",
        disasterLevel: "",
        disasterAreaCode: "",
      },
      tableData: [],
      eventDetailVisible: false,
      eventDetailData: {},

      // dict
      event_level: {},
      event_level_status,
      areaDict: {},
      enable_status: {
        Y: "已启用",
        N: "未启用",
      },
    };
  },
  computed: {},
  created() {
    this.getAreaList();
    getAndSetDict("event_level", this);
    this.fetch();
  },
  beforeDestroy() {
    cancelRoll(this.$refs.table);
  },
  methods: {
    async fetch() {
      const payload = {
        ...this.searchForm,
        provinceCode: "410100",
        pageNum: 1,
        pageSize: 999,
        startTime: this.dateRange[0] + " 00:00:00",
        endTime: this.dateRange[1] + " 23:59:59",
      };
      const [err, res] = await getEventList(payload);
      if (err) return;
      this.tableData = res.data?.records || [];
      this.$nextTick(() => {
        this.tableData.length > 15 && initRoll(this.$refs.table);
      });
    },
    async getAreaList() {
      const payload = {
        cityName: "郑州市",
      };
      const [err, res] = await getAreaList(payload);
      if (err) return;
      this.areaDict = (res.data || []).reduce((acc, cur) => {
        acc[cur.areaCode] = cur.areaName;
        return acc;
      }, {});
    },
    handleReset() {
      this.$refs.searchForm.resetFields();
      this.fetch();
    },
    handleDetail(row) {
      this.eventDetailData = row;
      this.$nextTick(() => {
        this.eventDetailVisible = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.event-list {
  .search-form::v-deep {
    @include flex-row;
    .el-form-item {
      @include flex-row;
      &.search-form_btn {
        flex: 0 0 auto;
      }
      .el-form-item__label {
        flex: 0 0 auto;
      }
    }
  }
}
</style>
