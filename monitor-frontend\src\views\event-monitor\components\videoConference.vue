<template>
  <div class="video-conference">
    <el-form ref="conferenceForm" :model="formModel" :rules="rules" class="content" @submit.native.prevent>
      <div class="top-section">
        <el-form-item label="会议主题" prop="theme" class="row like-row">
          <p class="row-label">会议主题</p>
          <el-input v-model="formModel.theme"></el-input>
        </el-form-item>
        <el-form-item label="会议类型" prop="type" class="row like-row" style="justify-content: start">
          <p class="row-label">会议类型</p>
          <el-radio-group v-model="formModel.type">
            <el-radio :label="0">预约会议</el-radio>
            <el-radio :label="1">快速会议</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="会议时间" prop="conferenceTime" class="row like-row" v-if="formModel.type === 0">
          <p class="row-label">会议时间</p>
          <el-date-picker
            v-model="formModel.conferenceTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            style="flex: 1; background: #003079"
          />
        </el-form-item>

        <div class="table-section" v-if="formModel.type === 0">
          <!-- <div class="row">
            <p class="row-label">会议主持</p>
             <el-button icon="el-icon-edit" type="primary"
              @click="handleAction(1)">选择主持</el-button>
          </div>
          <el-table :data="data1" :max-height="120" style="width: 100%">
            <el-table-column prop="organization" label="单位" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="contacts.phone" label="通讯方式" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <span class="action-button" @click="handleDelete(1)">删除</span>
              </template>
            </el-table-column>
          </el-table> -->
          <el-form-item label="会议密码" prop="password" class="row like-row">
            <p class="row-label">会议密码</p>
            <el-input
              v-model="formModel.password"
              minlength="4"
              maxlength="6"
              @input="onPasswordInput"
              placeholder="请输入4-6位数字"
              clearable
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="table-section flex-grow" v-if="formModel.type === 0">
        <div class="row">
          <p class="row-label">参会人员</p>
          <el-button icon="el-icon-plus" type="primary" @click="handleAction(0)"
            >增加人员</el-button
          >
        </div>
        <div class="table-container">
          <el-table :data="data2" height="100%" style="width: 100%">
            <el-table-column prop="organization" label="单位" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="contacts.phone" label="通讯方式" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <span class="action-button" @click="handleDelete(2, scope.row)"
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
// import DialogBox from "./DialogBox.vue";
// import Transfer from "../business/transfer.vue";
// import DrawerBox from "./DrawerBox.vue";
import dayjs from "dayjs";

export default {
  components: {
    // DialogBox,
    // Transfer,
    // DrawerBox
  },
  props: {
    data1: {
      type: Array,
      default: () => [], // 使用工厂函数返回空数组
    },
    data2: {
      type: Array,
      default: () => [], // 使用工厂函数返回空数组
    },
    initialtheme: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      formModel: {
        theme: this.initialtheme,
        type: 1,
        conferenceTime: [dayjs().format("YYYY-MM-DD HH:mm"), dayjs().format("YYYY-MM-DD HH:mm")],
        password: "",
      },
      rules: {
        theme: [{ required: true, message: "请输入会议主题", trigger: "blur" }],
        type: [{ required: true, message: "请选择会议类型", trigger: "change" }],
        conferenceTime: [
          {
            type: "array",
            required: true,
            message: "请选择会议时间",
            trigger: "change",
            // fields: {
            //   0: { type: "date", required: true, message: "请选择开始时间" },
            //   1: { type: "date", required: true, message: "请选择结束时间" },
            // },
          },
        ],
        password: [
          { validator: this.validatePassword, trigger: "blur" }
        ],
      },

      action: "",
    };
  },
  methods: {
    handleAction(type) {
      this.action = type;
      this.$emit("handleAction", type);
    },
    validatePassword(rule, value, callback) {
      if (value === '') {
        callback(new Error('请输入会议密码'));
      } else if (!/^\d{4,6}$/.test(value)) {
        callback(new Error('会议密码必须为4-6位数字'));
      } else {
        callback();
      }
    },
    // handleSubmit(list) {
    //   console.log("list: ", list);
    //   // this.showDialog = false;
    //   if (this.action === 1) {
    //     this.data1 = list;
    //   } else {
    //     this.data2 = list;
    //   }
    // },
    handleDelete(type, row) {
      if (type === 1) {
        // This logic might need adjustment if data1 becomes part of the form or needs validation
        this.data1 = this.data1.filter((item) => item.id !== row.id);
      } else {
        this.data2 = this.data2.filter((item) => item.id !== row.id);
      }
    },
    getConferenceInfo() {
      // Ensure form is valid before getting info
      return new Promise((resolve, reject) => {
        this.$refs.conferenceForm.validate((valid) => {
          if (valid) {
            const conferenceInfo = {
              type: this.formModel.type,
              subject: this.formModel.theme || "",
              password: this.formModel.password || "", // Include password
            };

            if (this.formModel.type === 0) {
              const [startTime, endTime] = this.formModel.conferenceTime;
              conferenceInfo.start_time = startTime
              conferenceInfo.end_time = endTime
            }
            resolve(conferenceInfo);
          } else {
            console.log("error submit!!");
            reject(new Error("Form validation failed"));
            return false;
          }
        });
      });
    },
    onPasswordInput(val) {
      // 只允许输入数字，且最大6位
      let filtered = (val + "").replace(/\D/g, "").slice(0, 6);
      this.formModel.password = filtered;
    },
    // validatePassword method is now replaced by el-form rules
    // validatePassword() {
    //   // 校验4-6位数字
    //   if (!/^\d{4,6}$/.test(this.formModel.password)) {
    //     // this.passwordError = "会议密码必须为4-6位数字"; // Error display handled by el-form-item
    //     return false;
    //   }
    //   // this.passwordError = "";
    //   return true;
    // },
    // Optional: Method to manually trigger form validation if needed from parent
    validateForm(callback) {
      this.$refs.conferenceForm.validate(callback);
    },
    // Optional: Method to reset form
    resetForm() {
      this.$refs.conferenceForm.resetFields();
      // Reset other data not part of formModel if necessary
      // this.data1 = [];
      this.data2 = []; // Assuming this should be cleared too
      this.qrCodeBase64 = null; // 新增：重置时清除二维码
    },
  },
  watch: {
    initialtheme(newVal) {
      this.formModel.theme = newVal;
    },
    'formModel.type'(newType) {
      if (newType === 1) { // 快速会议
        this.formModel.conferenceTime = [];
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.video-conference {
  @include full;
  display: flex;
  flex-direction: column;
  padding: 8px 16px;
  font-size: 14px;
  color: #fff;
  box-sizing: border-box;
  height: 100%;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0; // Adjusted for el-form-item margins
  overflow: hidden;
  padding-right: 8px;
  height: 100%;
}

.top-section {
  display: flex;
  flex-direction: column;
  gap: 0; // Adjusted for el-form-item margins
}

.table-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  &.flex-grow {
    flex: 1;
    min-height: 0;
    overflow: hidden;
  }
}

.table-container {
  flex: 1;
  height: 100%;
  min-height: 0;
  overflow: hidden;
}

.row {
  @include flex;
  justify-content: space-between;
  // Removed margin-bottom as el-form-item will handle spacing
  // margin-bottom: 24px; // Example: if el-form-item needs more space

  &-label {
    width: 100px;
    color: #fff; // Ensure label color
    margin-right: 10px; // Add some space between label and input
  }
}

// Specific styling for el-form-item to behave like the old .row
.like-row {
  display: flex;
  align-items: center;
  margin-bottom: 24px; // Restore original gap between rows

  :deep(.el-form-item__label) {
    // visibility: hidden; // Hide default el-form-item label as we use custom <p class="row-label">
    // width: 0;
    display: none; // Completely remove it from layout
  }

  :deep(.el-form-item__content) {
    flex: 1;
    display: flex; // Ensure content (p + input) aligns
    align-items: center; // Align items within the content
    margin-left: 0 !important; // Override default margin
  }

  &.el-form-item { // Target the el-form-item itself
    margin-bottom: 24px; // Default spacing for form items
  }
}

// 自定义表格样式
:deep(.el-table) {
  background-color: transparent;

  &::before {
    display: none;
  }

  th {
    background-color: #0080ff10;
    border-bottom: 1px solid #ffffff10;

    .cell {
      color: #fff;
    }
  }

  td {
    background-color: transparent;
    border-bottom: 1px solid #ffffff10;

    .cell {
      color: #fff;
    }
  }

  // 去除表格边框
  &,
  tr,
  th,
  td {
    border: none;
  }

  // 修改滚动条样式
  .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: #ffffff20;
    border-radius: 3px;
  }

  .el-table__body-wrapper::-webkit-scrollbar-track {
    background: #ffffff10;
    border-radius: 3px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 24px;
}

.el-button {
  font-size: 16px;
}

.action-button {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  color: #00ffff;
  cursor: pointer;
}

:deep(.el-input__inner) {
  background-color: #003079; // 设置为深蓝色背景
  color: #fff; // 确保文字颜色为白色
  border: none; // 移除默认边框
}

:deep(.el-range-editor .el-range-input) {
  background-color: #003079;
  color: #fff;
}

:deep(.el-range-editor .el-range-separator) {
  color: #fff;
}

:deep(.el-date-range-picker__time-header) {
  background-color: #003079;
  color: #fff;
}

:deep(.el-picker-panel__footer) {
  background-color: #003079;
  color: #fff;
  opacity: 0.9;
}

:deep(.el-date-picker) {
  background-color: #003079 !important; // 修改背景色
  color: #fff !important; // 修改文字颜色
  border: none !important; // 移除边框

  // 修改头部样式
  .el-picker-panel__header {
    background-color: #003079 !important;
    color: #fff !important;
  }

  // 修改单元格样式
  .el-date-table td {
    color: #fff !important;
  }

  // 修改当前日期样式
  .el-date-table td.current:not(.disabled) span {
    color: #003079 !important;
    background-color: #fff !important;
  }

  // 修改选中日期样式
  .el-date-table td.selected span {
    background-color: #fff !important;
    color: #003079 !important;
  }

  // 修改悬停日期样式
  .el-date-table td.available:hover {
    color: #003079 !important;
  }

  // 修改输入框样式
  .el-range-editor {
    background-color: #003079 !important;
    color: #fff !important;
    border: none !important;

    .el-range-input {
      background-color: transparent !important;
      color: #fff !important;
    }

    .el-range-separator {
      color: #fff !important;
    }
  }
}

// 添加会议类型单选框样式
:deep(.el-radio) {
  margin-right: 20px;
  color: #fff;

  .el-radio__label {
    color: #fff;
  }

  .el-radio__input.is-checked .el-radio__inner {
    background-color: #00ffff;
    border-color: #00ffff;
  }

  .el-radio__input.is-checked + .el-radio__label {
    color: #00ffff;
  }
}
</style>
